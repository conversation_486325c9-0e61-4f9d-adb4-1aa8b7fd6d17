import { useNavigate } from 'react-router-dom';
import { useListJobSimulationAppScreenByUserAndApp } from '~/data-provider/JobSimulation';
import type { TUser } from 'librechat-data-provider';
import type t from 'librechat-data-provider';
import { Skeleton, Popover, PopoverContent, PopoverTrigger } from '~/components/ui';
import { useEffect, useState, useRef } from 'react';
import AddScreensModal from './AddScreensModal';
import {
  useCreateJobSimulationAppScreen,
  useDeleleJobSimulationAppScreen,
  useUpdateJobSimulationAppScreen,
  useUpdateJobSimulationAppScreenOrder,
} from '~/data-provider/JobSimulation/mutations';
import { useToastContext } from '~/Providers';
import { EditIcon, DeleteIcon, PlusIcon } from '~/components/svg';
import EditScreenModal from './EditScreenModal';
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
  DragStartEvent,
  DragOverlay,
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  rectSortingStrategy,
} from '@dnd-kit/sortable';
import SortableScreenItem from './SortableScreenItem';

interface IScreen {
  appSelected: t.TJobSimulationApp | null;
  user: TUser | undefined;
}

export default function Screen({ appSelected, user }: IScreen) {
  const navigate = useNavigate();
  const [openAddScreenModal, setOpenAddScreenModal] = useState<boolean>(false);
  const [openEditScreenModal, setOpenEditScreenModal] = useState<boolean>(false);
  const [selectedScreen, setSelectedScreen] = useState<any>(null);
  const [openDeletePopover, setOpenDeletePopover] = useState<string | null>(null);
  const [screens, setScreens] = useState<any[]>([]);
  const [activeId, setActiveId] = useState<string | null>(null);
  const [isDragging, setIsDragging] = useState(false);

  const previousScreensRef = useRef<any[]>([]);
  const { showToast } = useToastContext();

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    }),
  );

  const {
    data: screenData,
    isLoading,
    refetch,
  } = useListJobSimulationAppScreenByUserAndApp({
    userId: user?.id as string,
    appId: appSelected?._id as string,
    page: 1,
    limit: 100,
  });

  useEffect(() => {
    if (screenData?.screens) {
      setScreens(screenData.screens);
    }
  }, [screenData]);

  const addScreen = useCreateJobSimulationAppScreen({
    onSuccess: () => {
      refetch();
      setOpenAddScreenModal(false);
      showToast({
        message: 'Screens added successfully',
        status: 'success',
      });
    },
    onError: () => {
      showToast({
        message: 'Failed to add screens',
        status: 'error',
      });
    },
  });

  const updateScreen = useUpdateJobSimulationAppScreen({
    onSuccess: () => {
      refetch();
      setOpenEditScreenModal(false);
      setSelectedScreen(null);
      showToast({
        message: 'Screen updated successfully',
        status: 'success',
      });
    },
    onError: () => {
      showToast({
        message: 'Failed to update screen',
        status: 'error',
      });
    },
  });

  const deleteScreen = useDeleleJobSimulationAppScreen({
    onSuccess: () => {
      refetch();
      setOpenDeletePopover(null);
      showToast({
        message: 'Screen deleted successfully',
        status: 'success',
      });
    },
    onError: () => {
      showToast({
        message: 'Failed to delete screen',
        status: 'error',
      });
    },
  });

  const updateScreenOrder = useUpdateJobSimulationAppScreenOrder({
    onSuccess: () => {
      showToast({
        message: 'Screen order updated successfully',
        status: 'success',
      });
    },
    onError: () => {
      setScreens(previousScreensRef.current);
      showToast({
        message: 'Failed to update screen order',
        status: 'error',
      });
    },
  });

  const handleScreenClick = (screen) => {
    if (!isDragging) {
      navigate(`/job-simulation/setting/${appSelected?._id}/${screen._id}`);
    }
  };

  const handleAddScreens = (files: File[]) => {
    addScreen.mutate({
      userId: user?.id as string,
      appId: appSelected?._id as string,
      files: files,
    });
  };

  const handleEditSubmit = (screenId, params) => {
    updateScreen.mutate({
      id: screenId,
      userId: user?.id as string,
      appId: appSelected?._id as string,
      file: params.file,
      name: params.name,
    });
  };

  const handleEditScreen = (screen) => {
    setSelectedScreen(screen);
    setOpenEditScreenModal(true);
  };

  const handleDeleteScreen = (e, screen) => {
    e.stopPropagation();
    deleteScreen.mutate({
      id: screen._id,
      userId: user?.id as string,
      appId: appSelected?._id as string,
    });
  };

  const handleDragStart = (event: DragStartEvent) => {
    previousScreensRef.current = [...screens];
    setActiveId(event.active.id as string);
    setIsDragging(true);
  };

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;
    setIsDragging(false);
    setActiveId(null);

    if (over && active.id !== over.id) {
      const oldIndex = screens.findIndex((screen) => screen._id === active.id);
      const newIndex = screens.findIndex((screen) => screen._id === over.id);

      const draggedScreen = screens[oldIndex];
      const replacedScreen = screens[newIndex];

      const newScreens = [...screens];
      newScreens[oldIndex] = replacedScreen;
      newScreens[newIndex] = draggedScreen;
      setScreens(newScreens);

      const updatedScreens = [
        {
          id: draggedScreen._id,
        },
        {
          id: replacedScreen._id,
        },
      ];

      updateScreenOrder.mutate({
        userId: user?.id as string,
        appId: appSelected?._id as string,
        screens: updatedScreens,
      });
    }
  };

  const activeScreen = activeId ? screens.find((screen) => screen._id === activeId) : null;

  const isUpdatingOrder = updateScreenOrder.isLoading;

  return (
    <>
      <div className="space-y-6">
        <div className="mb-4">
          <h2 className="text-2xl font-bold text-black dark:text-white">App Screens</h2>
          <p className="text-gray-500">Manage and organize the screens for your simulation app</p>
        </div>

        <div className="flex flex-wrap gap-5">
          <div
            className="group flex h-44 w-72 flex-col items-center justify-center overflow-hidden rounded-xl border-2 border-dashed border-gray-300 bg-gray-50 transition-all duration-200 hover:cursor-pointer hover:border-purple-500 hover:bg-purple-50"
            onClick={() => setOpenAddScreenModal(true)}
          >
            <div className="mb-2 rounded-full bg-purple-100 p-3 text-purple-500 transition-all duration-200 group-hover:bg-purple-200">
              <PlusIcon className="h-8 w-8" />
            </div>
            <div className="font-medium text-gray-700 group-hover:text-purple-600">
              Add new Screens
            </div>
            <p className="mt-1 text-xs text-gray-500">Select up to 12 images</p>
          </div>

          {isLoading ? (
            [...Array(3)].map((_, index) => (
              <div key={index} className="relative h-44 w-72 overflow-hidden rounded-xl shadow-md">
                <Skeleton className="h-full w-full rounded-xl" />
                <div className="absolute bottom-0 left-0 right-0 h-10 bg-gradient-to-t from-gray-200 to-transparent"></div>
              </div>
            ))
          ) : screens.length > 0 ? (
            <DndContext
              sensors={sensors}
              collisionDetection={closestCenter}
              onDragStart={isUpdatingOrder ? undefined : handleDragStart}
              onDragEnd={isUpdatingOrder ? undefined : handleDragEnd}
            >
              <SortableContext
                items={screens.map((screen) => screen._id)}
                strategy={rectSortingStrategy}
              >
                <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4">
                  {screens.map((screen) => (
                    <div
                      key={screen._id}
                      onClick={() => handleScreenClick(screen)}
                      className="transform transition duration-200 hover:scale-105"
                    >
                      <SortableScreenItem
                        screen={screen}
                        onEdit={handleEditScreen}
                        onDelete={handleDeleteScreen}
                        openDeletePopover={openDeletePopover}
                        setOpenDeletePopover={setOpenDeletePopover}
                        isDisabled={isUpdatingOrder}
                      />
                    </div>
                  ))}
                </div>
              </SortableContext>

              <DragOverlay adjustScale={true}>
                {activeId && activeScreen && (
                  <div className="relative h-44 w-72 overflow-hidden rounded-xl border-2 border-purple-500 bg-black shadow-xl">
                    <img
                      src={activeScreen.image}
                      alt="dragging"
                      className="h-full w-full object-cover opacity-90"
                    />
                    <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black to-transparent px-3 py-2">
                      <p
                        className="truncate text-sm font-medium text-white"
                        title={activeScreen.name || 'Unnamed screen'}
                      >
                        {activeScreen.name || 'Unnamed screen'}
                      </p>
                    </div>
                  </div>
                )}
              </DragOverlay>
            </DndContext>
          ) : (
            <div className="flex h-44 w-72 flex-col items-center justify-center rounded-xl border border-gray-300 bg-gray-50 p-6 text-center">
              <div className="mb-2 rounded-full bg-gray-100 p-3">
                <img
                  src="/images/empty-state.svg"
                  alt="No screens"
                  className="h-10 w-10 opacity-50"
                  onError={(e) => (e.currentTarget.style.display = 'none')}
                />
              </div>
              <p className="text-gray-500">No screens available</p>
              <p className="mt-1 text-xs text-gray-400">Add some screens to get started</p>
            </div>
          )}
        </div>

        {openAddScreenModal && (
          <AddScreensModal
            open={openAddScreenModal}
            onClose={() => setOpenAddScreenModal(false)}
            onAdd={handleAddScreens}
            maxFiles={12}
            disabled={addScreen.isLoading}
          />
        )}

        {openEditScreenModal && selectedScreen && (
          <EditScreenModal
            open={openEditScreenModal}
            onClose={() => {
              setOpenEditScreenModal(false);
              setSelectedScreen(null);
            }}
            onEdit={handleEditSubmit}
            screen={selectedScreen}
            disabled={updateScreen.isLoading}
          />
        )}
      </div>
    </>
  );
}
