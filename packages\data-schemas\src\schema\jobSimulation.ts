import { Document, Schema } from 'mongoose';

export interface IJobSimulation extends Document {
  // TODO: change to jobsimUniqueId
  jobSimulationId: string;
  name: string;
  role?: string;
  description?: string;
  banner?: string;
  logo: string;
  companyName?: string;
  billionIntakeCode?: string;
  billionIntakeId?: string;
  agentId: string;
  agentInstructions: string;
  virtualWorld?: string;
  skills?: string[];
  credentials: {
    username: string;
    password: string;
  };
  /**
   * Public: everyone can access,
   * Private: only users know the link can access. TODO: implement logic to generate invitation code
   * Inactive: no one can access
   */
  status: 'public' | 'private' | 'inactive';
  appIds?: string[];
}

const jobSimulationSchema: Schema<IJobSimulation> = new Schema({
  // TODO: change to jobsimUniqueId
  jobSimulationId: {
    type: String,
    required: true,
  },
  name: {
    type: String,
    required: true,
  },
  role: {
    type: String,
    required: false,
  },
  skills: {
    type: [String],
    required: false,
    default: undefined,
  },
  description: {
    type: String,
    required: false,
  },
  banner: {
    type: String,
    required: false,
  },
  logo: {
    type: String,
    required: true,
  },
  companyName: {
    type: String,
    required: false,
  },
  billionIntakeCode: {
    type: String,
    required: false,
  },
  billionIntakeId: {
    type: String,
    required: false,
  },
  agentId: {
    type: String,
    required: true,
  },
  agentInstructions: {
    type: String,
    required: true,
  },
  virtualWorld: {
    type: String,
    required: false,
  },
  credentials: {
    username: {
      type: String,
      required: true,
    },
    password: {
      type: String,
      required: true,
    },
  },
  status: {
    // Using enum
    type: String,
    enum: ['public', 'private', 'inactive'],
    required: true,
    default: 'private',
  },
  appIds: {
    type: [String],
    required: false,
  },
});

jobSimulationSchema.index({ jobSimulationId: 1 }, { unique: true });

export default jobSimulationSchema;
