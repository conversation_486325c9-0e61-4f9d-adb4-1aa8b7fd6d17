{"name": "server", "version": "1.0.0", "type": "module", "scripts": {"dev": "nodemon --exec tsx index.ts", "build": "tsc", "start": "node dist/index.js", "postinstall": "npx playwright install"}, "dependencies": {"@ai-sdk/openai": "^1.3.23", "@browserbasehq/sdk": "^2.6.0", "@browserbasehq/stagehand": "^2.4.1", "@playwright/test": "^1.49.1", "@types/ws": "^8.18.1", "ai": "^4.3.19", "dotenv": "^16.4.7", "sharp": "^0.34.3", "uWebSockets.js": "github:uNetworking/uWebSockets.js#v20.52.0", "zod": "^3.25.76"}, "devDependencies": {"@types/node": "^20.10.0", "nodemon": "^3.0.2", "tsx": "^4.19.2", "typescript": "^5.0.0"}}