interface SocketUserData {
    userId: string;
    userName: string;
}

interface Step {
    text: string;
    reasoning: string;
    tool: "GOTO" | "ACT" | "EXTRACT" | "OBSERVE" | "CLOSE" | "WAIT" | "NAVBACK";
    instruction: string;
};

interface BrowserClickEvent {
    type: 'click';
    data: {
        x: number;
        y: number;
    }
}

interface BrowserReloadEvent {
    type: 'reload';
    data: {
        x: number;
        y: number;
    }
}

interface BrowserScrollYEvent {
    type: 'scroll';
    data: {
        direction: 'up' | 'down';
    }
}

interface BrowserStartEvent {
    type: 'start_browser';
    data: {
        url?: string;
        timezone?: string;
    }
}

interface BrowserCloseEvent {
    type: 'close_browser';
}

interface BrowserStartDemoEvent {
    type: 'start_demo';
}

interface BrowserInstructionEvent {
    type: 'agent_instruction';
    data: {
        instruction: string;
    }
}

interface BrowserGoalEvent {
    type: 'goal';
    data: {
        goal: string;
        action: "START" | "GET_NEXT_STEP" | "EXECUTE_STEP";
        previousSteps: Step[];
        step?: Step;
    }
}

type BrowserClientEvent = BrowserClickEvent | BrowserStartEvent | BrowserStartDemoEvent | BrowserInstructionEvent | BrowserCloseEvent | BrowserReloadEvent | BrowserScrollYEvent | BrowserGoalEvent;

export {
    SocketUserData,
    BrowserClientEvent,
    Step,
}