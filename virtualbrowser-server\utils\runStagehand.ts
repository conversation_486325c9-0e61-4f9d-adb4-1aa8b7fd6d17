import { Stagehand } from "@browserbasehq/stagehand";

const runStagehand = async ({
    stagehand,
    method,
    instruction,
}: {
    stagehand: Stagehand;
    method:
    | "GOTO"
    | "ACT"
    | "EXTRACT"
    | "CLOSE"
    | "SCREENSHOT"
    | "OBSERVE"
    | "WAIT"
    | "NAVBACK";
    instruction?: string;
}) => {

    const page = stagehand.page;

    try {
        switch (method) {
            case "GOTO":
                await page.goto(instruction!, {
                    waitUntil: "commit",
                    timeout: 60000,
                });
                break;

            case "ACT":
                await page.act(instruction!);
                break;

            case "EXTRACT": {
                const { extraction } = await page.extract(instruction!);
                return extraction;
            }

            case "OBSERVE":
                return await page.observe(instruction!);

            case "CLOSE":
                // await stagehand.close();
                break;

            case "WAIT":
                await new Promise((resolve) =>
                    setTimeout(resolve, Number(instruction))
                );
                break;

            case "NAVBACK":
                await page.goBack();
                break;
        }
    } catch (error) {
        await stagehand.close();
        throw error;
    }
}

export default runStagehand;