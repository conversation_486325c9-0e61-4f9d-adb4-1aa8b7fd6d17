import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { Popover, PopoverContent, PopoverTrigger } from '~/components/ui';
import { EditIcon, DeleteIcon } from '~/components/svg';

export default function SortableScreenItem({
  screen,
  onEdit,
  onDelete,
  openDeletePopover,
  setOpenDeletePopover,
  isDisabled = false,
}) {
  const { attributes, listeners, setNodeRef, transform, transition, isDragging } = useSortable({
    id: screen._id,
    disabled: isDisabled,
  });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.3 : isDisabled ? 1 : 1,
    zIndex: isDragging ? 0 : 1,
  };

  const handleEditScreen = (e) => {
    e.stopPropagation();
    onEdit(screen);
  };

  const handleDeleteClick = (e) => {
    e.stopPropagation();
    if (!isDisabled) {
      setOpenDeletePopover(screen._id);
    }
  };

  const handleDeleteConfirm = (e) => {
    e.stopPropagation();
    onDelete(e, screen);
  };

  const handleCancelDelete = (e) => {
    e.stopPropagation();
    setOpenDeletePopover(null);
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={`group relative flex h-44 w-72 items-center justify-center overflow-hidden rounded-xl border border-gray-300 bg-black ${
        isDisabled ? 'cursor-not-allowed' : 'hover:cursor-pointer'
      }`}
      {...attributes}
    >
      {!isDisabled && (
        <div
          className="absolute left-2 top-2 z-10 flex h-8 w-8 cursor-move items-center justify-center rounded-full bg-gray-800 bg-opacity-70 opacity-0 transition-opacity group-hover:opacity-100"
          {...listeners}
        >
          <svg
            className="h-4 w-4 text-white"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              d="M4 8h16M4 16h16"
            />
          </svg>
        </div>
      )}

      <div className="absolute right-2 top-2 z-10 flex gap-1 opacity-0 transition-opacity group-hover:opacity-100">
        <button
          className={`flex h-8 w-8 items-center justify-center rounded-full bg-gray-800 bg-opacity-70 p-1 text-white ${
            isDisabled ? 'cursor-not-allowed opacity-50' : 'hover:bg-gray-700 hover:text-white'
          }`}
          onClick={handleEditScreen}
          title="Edit screen"
          disabled={isDisabled}
        >
          <EditIcon className="h-4 w-4" />
        </button>

        <Popover
          open={openDeletePopover === screen._id}
          onOpenChange={(open) => {
            if (!isDisabled) {
              setOpenDeletePopover(open ? screen._id : null);
            }
          }}
        >
          <PopoverTrigger asChild>
            <button
              className={`flex h-8 w-8 items-center justify-center rounded-full bg-gray-800 bg-opacity-70 p-1 text-white ${
                isDisabled ? 'cursor-not-allowed opacity-50' : 'hover:bg-red-600'
              }`}
              title="Delete screen"
              onClick={handleDeleteClick}
              disabled={isDisabled}
            >
              <DeleteIcon className="h-4 w-4" />
            </button>
          </PopoverTrigger>
          <PopoverContent
            className="z-50 w-fit rounded-lg border border-gray-200 bg-white p-2 shadow-lg"
            align="center"
          >
            <div className="w-fit">Are you sure you want to delete this screen?</div>
            <div className="mt-2 flex">
              <button
                className="mr-2 rounded bg-gray-200 px-3 py-1 text-sm text-gray-700 hover:bg-gray-300"
                onClick={handleCancelDelete}
              >
                Cancel
              </button>
              <button
                className="rounded bg-red-600 px-3 py-1 text-sm text-white hover:bg-red-700"
                onClick={handleDeleteConfirm}
              >
                Remove
              </button>
            </div>
          </PopoverContent>
        </Popover>
      </div>

      <img src={screen.image} alt="screen" className="h-full w-full object-cover" />

      <div className="absolute bottom-0 left-0 right-0 flex h-6 items-center bg-black bg-opacity-70 px-2">
        <p className="truncate text-sm text-white" title={screen.name || 'Unnamed screen'}>
          {screen.name || 'Unnamed screen'}
        </p>
      </div>
    </div>
  );
}
