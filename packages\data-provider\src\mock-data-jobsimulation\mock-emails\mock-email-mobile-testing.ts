import { TJobSimulationEmail } from 'src/types';

const getEmails = (data: {
  jobSimulationId: string;
  logo: string;
  billionIntakeId?: string;
}): TJobSimulationEmail[] => [
  {
    id: '1',
    name: 'HR AppVerify Inc.',
    avatar: 'https://d14ciuzrn5ydd5.cloudfront.net/bitmeet/tutor-ai/image-3.jpg',
    role: 'HR Manager',
    email: '<EMAIL>',
    title: 'Welcome to AppVerify Inc. Mobile Testing Simulation!',
    desc: "Welcome aboard! We're thrilled to have you join our QA team at AppVerify Inc.",
    nextEmailId: '2',
    data: {
      logo: data.logo,
      greeting: 'Hi {user_name}',
      content: `I'm **HR Manager at AppVerify Inc.**, and I'm excited you're joining our team!

This simulation will immerse you in the daily tasks of a mobile QA tester — from identifying bugs to writing test cases.

To kick things off, let's meet virtually for a quick orientation meeting where you'll learn what to expect and who you'll be working with.

I'll be scheduling the meeting shortly. Looking forward to seeing you!`,
      signature: {
        title: 'Regards',
        company: 'AppVerify Inc.',
      },
    },
    triggerActions: [
      {
        type: 'nextEmail',
        data: { nextEmailId: '2', triggerTimeout: 3, when: 'open' },
      },
      {
        type: 'enableApps',
        data: {
          appIds: ['mail', 'news', 'meeting'],
          when: 'receive',
        },
      },
    ],
  },
  {
    id: '2',
    name: 'Emma Kate',
    role: 'QA Lead',
    avatar: 'https://beta.bitmeet.io/files/1752675663848-*********-emmakate_mobile_testing.jpg',
    email: '<EMAIL>',
    title: 'Introductory Meeting Invitation',
    desc: 'Learn about your responsibilities and how we test apps at AppVerify Inc. in a short onboarding session.',
    nextEmailId: '3',
    data: {
      logo: data.logo,
      greeting: 'Hi {user_name}',
      content: `I'm **Emma Kate**, QA Lead at AppVerify Inc.

You're about to begin your journey as a QA Tester, and we've got a great set of real-world tasks lined up for you.

Before diving in, let's have a quick meeting to:
* Introduce you to the QA workflow
* Explain your tasks
* Align expectations and tools you'll be using

See you shortly!`,
      actions: [
        {
          type: 'joinMeeting',
          label: 'Join the Meeting',
          title: 'Onboarding Meeting',
          data: {
            datetime: '{email_time}',
            duration: '~1 minutes',
            meetingLink: 'https://dev-bitmeet.mnet.io/introduction/4a4-fa8-db4?t=mi',
            completionMeetingActions: [
              {
                type: 'triggerAssistant',
                data: {
                  triggerMessage: 'I have completed the meeting with Emma.',
                },
              },
              {
                type: 'enableApps',
                data: {
                  appIds: ['mail', 'news', 'meeting', 'task-board', 'todoist', 'jira'],
                },
              },
              {
                type: 'sendEmailTask',
              },
            ],
          },
        },
      ],
      signature: {
        title: 'Warm regards',
        company: 'AppVerify Inc.',
      },
    },
    triggerActions: [
      {
        type: 'triggerAssistant',
        data: {
          triggerTimeout: 1,
          triggerMessage: "I've received the meeting invitation.",
          when: 'receive',
        },
      },
    ],
  },
  {
    id: '3',
    name: 'Emma Kate',
    role: 'QA Lead',
    avatar: 'https://beta.bitmeet.io/files/1752675663848-*********-emmakate_mobile_testing.jpg',
    email: '<EMAIL>',
    title: 'Thank You for Completing the Mobile QA Simulation',
    desc: "Well done! You've completed all your tasks in the Mobile QA Simulation.",
    data: {
      logo: data.logo,
      greeting: 'Hi {user_name}',
      content: `You've successfully completed the **Mobile App QA Simulation** at AppVerify Inc.

You've shown attention to detail, strong testing instincts, and effective communication skills.

Thank you for participating in this simulation!

Here's your reference letter:`,
      actions: [
        {
          type: 'viewFileDetail',
          label: 'View Reference Letter',
          title: 'Reference Letter',
          data: {
            fileUrl: `https://uat.internship.guru/en/public/reference-letter?programId=${data.billionIntakeId}&autoClaim=true`,
          },
        },
      ],
      signature: {
        title: 'Regards',
        company: 'AppVerify Inc.',
      },
    },
  },
];

// Build an email task, sent from Emma Kate (QA Lead)
const buildEmailTask = (prams: { jobSimulation: any; task: any }): TJobSimulationEmail => {
  const { jobSimulation, task } = prams;
  return {
    id: `email-task:${task.taskId}`,
    type: 'task',
    name: 'Emma Kate',
    avatar: 'https://beta.bitmeet.io/files/1752675663848-*********-emmakate_mobile_testing.jpg',
    role: 'QA Lead',
    email: '<EMAIL>',
    title: `Task: ${task.taskName}`,
    desc: `New task assigned: ${task.taskName}`,
    data: {
      taskId: task.taskId,
      programId: jobSimulation.billionIntakeId,
      logo: jobSimulation.logo,
      greeting: 'Hi {user_name}',
      content: `You're doing great so far in the **Mobile QA Simulation** at AppVerify Inc.!

Your next task is titled: **${task.taskName}**

${task.taskDescription}

Please review the task carefully and complete it at your earliest convenience.
**You can reply directly to this email with your submission.**

Good luck — we're excited to see your QA skills in action!`,
      actions: [],
      signature: {
        title: 'Best regards',
        company: jobSimulation.companyName,
      },
    },
    allowReply: true,
    triggerActions: [
      {
        type: 'enableApps',
        data: {
          appIds: ['mail', 'news', 'meeting', 'task-board', 'todoist', 'jira'],
          when: 'receive',
        },
      },
    ],
  };
};

export default { getEmails, buildEmailTask };
