import { facebookAdCalculationConfig, fbAdScreens } from '~/utils/appSimulationConfig/fbadConfig';
import { ggAdCalculationConfig, ggAdScreens } from '~/utils/appSimulationConfig/ggadConfig';
import { jiraScreens } from '~/utils/appSimulationConfig/jiraConfig';
import { powerBIScreens } from '~/utils/appSimulationConfig/powerbiConfig';
import { todoistScreens } from '~/utils/appSimulationConfig/todoistConfig';
import { xAdCalculationConfig, xAdScreens } from '~/utils/appSimulationConfig/xadConfig';

export const getAppData = (appSimulationId: string) => {
  if (appSimulationId === 'facebook-ad') {
    return {
      appSimulationScreens: fbAdScreens,
      appSimulationConfig: facebookAdCalculationConfig,
    };
  } else if (appSimulationId === 'x-ad') {
    return {
      appSimulationScreens: xAdScreens,
      appSimulationConfig: xAdCalculationConfig,
    };
  } else if (appSimulationId === 'google-ads') {
    return {
      appSimulationScreens: ggAdScreens,
      appSimulationConfig: ggAdCalculationConfig,
    };
  } else if (appSimulationId === 'powerbi') {
    return {
      appSimulationScreens: powerBIScreens,
      // appSimulationConfig: null,
    };
  } else if (appSimulationId === 'todoist') {
    return {
      appSimulationScreens: todoistScreens,
    };
  } else if (appSimulationId === 'jira') {
    return {
      appSimulationScreens: jiraScreens,
    };
  }

  return null;
};
