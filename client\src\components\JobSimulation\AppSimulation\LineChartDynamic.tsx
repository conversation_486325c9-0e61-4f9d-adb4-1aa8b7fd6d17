import { useEffect, useRef, useState } from 'react';
import {
  CartesianGrid,
  Line,
  LineChart,
  ResponsiveContainer,
  <PERSON>lt<PERSON>,
  XAxis,
  YAxis,
} from 'recharts';
import { AppSimulationDynamicChartData } from '~/common';

interface LineChartDynamicProps {
  data: AppSimulationDynamicChartData;
  chartArea: {
    x1: number;
    y1: number;
    x2: number;
    y2: number;
  };
  // Get dynamic data, sync with placeholder or data that user has input
  getCurrentValue: (dataId: string) => string | number;
}

interface ChartDataPoint {
  [key: string]: string | number;
}

const LineChartDynamic = (props: LineChartDynamicProps) => {
  const {
    chartArea,
    data: { timePoints, xLabels, labelX, labelY, lines },
    getCurrentValue,
  }: LineChartDynamicProps = props;

  const [elapsedTime, setElapsedTime] = useState(0);
  const [chartData, setChartData] = useState<ChartDataPoint[]>([]);
  const [startTime] = useState<number>(Date.now());
  const [processedTimePoints, setProcessedTimePoints] = useState<Set<number>>(new Set());
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  // Timer
  useEffect(() => {
    intervalRef.current = setInterval(() => {
      const currentTime = Date.now();
      const elapsed = Math.floor((currentTime - startTime) / 1000);
      setElapsedTime(elapsed);
    }, 1000);

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    };
  }, [startTime]);

  useEffect(() => {
    if (processedTimePoints.size === timePoints.length && intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
  }, [processedTimePoints.size, timePoints.length]);

  useEffect(() => {
    const newTimePoints = timePoints.filter(
      (timePoint) => timePoint <= elapsedTime && !processedTimePoints.has(timePoint),
    );

    if (newTimePoints.length > 0) {
      const newChartData = [...chartData];

      newTimePoints.forEach((timePoint) => {
        const timeIndex = timePoints.indexOf(timePoint);
        // const xLabel = xLabels[timeIndex] || `${timePoint}s`;

        // Create new data point
        // const dataPoint: ChartDataPoint = {
        //   [labelX]: xLabel,
        // };

        const dataPoint = {};

        lines.forEach((line) => {
          let value: number;

          if (line.dataId) {
            // Case 1: Get value from dataId
            const currentValue = getCurrentValue(line.dataId);
            value = typeof currentValue === 'string' ? parseFloat(currentValue) || 0 : currentValue;
          } else if (line.staticValues && line.staticValues[timeIndex] !== undefined) {
            // Case 2: Get hardcoded data
            value = line.staticValues[timeIndex];
          } else {
            value = 0;
          }

          dataPoint[line.name] = value;
          dataPoint[labelX] = xLabels[timeIndex];
        });

        newChartData.push(dataPoint);
      });

      // console.log('newChartData ::: ', newChartData);

      // newChartData.sort((a, b) => {
      //   const aIndex = xLabels.indexOf(a[labelX] as string);
      //   const bIndex = xLabels.indexOf(b[labelX] as string);
      //   return aIndex - bIndex;
      // });

      // console.log('sort ::: after ::: ', newChartData);

      setChartData(newChartData);
      setProcessedTimePoints(new Set([...processedTimePoints, ...newTimePoints]));
    }
  }, [
    elapsedTime,
    timePoints,
    xLabels,
    labelX,
    lines,
    getCurrentValue,
    chartData,
    processedTimePoints,
  ]);

  return (
    <div
      className="relative"
      style={{
        position: 'absolute',
        left: `${chartArea.x1}%`,
        top: `${chartArea.y1}%`,
        zIndex: 15,
        pointerEvents: 'auto',
        backgroundColor: '#fff',
        width: `${chartArea.x2 - chartArea.x1}%`,
        height: `${chartArea.y2 - chartArea.y1}%`,
      }}
    >
      <ResponsiveContainer width="100%" height="100%">
        <LineChart
          style={{
            width: `100%`,
            maxHeight: `100%`,
          }}
          data={chartData}
        >
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis fontSize={13} label={labelX} dataKey={labelX} padding={{ left: 5, right: 5 }} />
          <YAxis fontSize={13} />
          <Tooltip labelStyle={{ fontSize: '1rem' }} itemStyle={{ fontSize: '1rem' }} />
          {lines.map((line) => (
            <Line
              key={line.name}
              type={line.type || 'monotone'}
              dataKey={line.name}
              stroke={line.stroke || '#8884d8'}
              activeDot={{ r: 8 }}
            />
          ))}
        </LineChart>
      </ResponsiveContainer>
      {/* Debug info - can be removed in production */}
      {/* {process.env.NODE_ENV === 'development' && (
        <div
          style={{
            position: 'absolute',
            top: '-30px',
            left: '0',
            fontSize: '10px',
            color: '#666',
            background: 'rgba(255,255,255,0.8)',
            padding: '2px 4px',
            borderRadius: '2px',
          }}
        >
          Time: {elapsedTime}s | Points: {chartData.length}/{timePoints.length}
        </div>
      )} */}
    </div>
  );
};

export default LineChartDynamic;
