import { useOutletContext } from 'react-router-dom';
import { useRecoilValue, useSetRecoilState } from 'recoil';
import { TJobSimulationContext } from '~/common';
import { useGetEmailTaskAssignment } from '~/data-provider/JobSimulation';
import useOpenCloseApp from '~/hooks/useOpenCloseApp';
import store from '~/store';

export default function useGetEmailTask() {
  const { jobSimulationId } = useOutletContext<TJobSimulationContext>();
  const jobSimulationUser = useRecoilValue(store.jobSimulationUser);
  const setJobSimulationEmails = useSetRecoilState(store.jobSimulationEmails);
  const { openApp } = useOpenCloseApp();
  const { refetch } = useGetEmailTaskAssignment(jobSimulationId || '', {
    enabled: false,
  });
  // { ...taskEmail, read: false, time: Date.now() }
  const getEmailTask = async () => {
    if (!jobSimulationUser) return;
    const result = await refetch();
    if ((result?.data as any)?.id) {
      const taskEmail = (result?.data as any) || {};
      setJobSimulationEmails((prevEmails) => {
        const checkExist = prevEmails.find((email) => email.id === taskEmail.id);
        if (checkExist) return prevEmails;
        return [...prevEmails, { ...taskEmail, read: false, time: Date.now() }];
      });
      openApp('mail');
    }
  };

  return { getEmailTask };
}
