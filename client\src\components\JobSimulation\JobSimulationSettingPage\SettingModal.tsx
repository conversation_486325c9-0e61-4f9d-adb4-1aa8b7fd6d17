import { useRef } from 'react';
import {
  Button,
  Dialog,
  Input,
  OGDialogTemplate,
  SelectDropDown,
  Switch,
  Checkbox,
} from '~/components/ui';
import { useForm, Controller, useFieldArray } from 'react-hook-form';

interface SettingModalProps {
  open: boolean;
  onClose: () => void;
  onOk: (data: any, type: string) => void;
  screens: any[];
  type?: string;
  element?: any;
}

const contentTypes = [
  { value: 'elements', label: 'Element' },
  { value: 'charts', label: 'Chart' },
  { value: 'placeholders', label: 'Placeholder' },
];

const actionTypes = [
  { value: 'nextScreen', label: 'Next Screen' },
  { value: 'inputText', label: 'Input Text' },
  { value: 'dropdown', label: 'Dropdown' },
  { value: 'modal', label: 'Modal' },
  { value: 'triggerMessage', label: 'Trigger Message' },
  { value: 'uploadPhoto', label: 'Upload Photo' },
];

const inputTypes = [
  { value: 'textarea', label: 'Textarea' },
  { value: 'text', label: 'Text' },
];

const placeholderTypes = [
  { value: 'text', label: 'Text' },
  { value: 'image', label: 'Image' },
];

const fontWeights = [
  { value: 'normal', label: 'Normal' },
  { value: 'bold', label: 'Bold' },
  { value: 'bolder', label: 'Bolder' },
  { value: 'lighter', label: 'Lighter' },
];

const alignItems = [
  { value: 'normal', label: 'Normal' },
  { value: 'stretch', label: 'Stretch' },
  { value: 'flex-start', label: 'Flex-Start' },
  { value: 'flex-end', label: 'Flex-End' },
  { value: 'start', label: 'Start' },
  { value: 'end', label: 'End' },
  { value: 'baseline', label: 'Baseline' },
  { value: 'initial', label: 'Initial' },
  { value: 'inherit', label: 'Inherit' },
];

const justifyContents = [
  { value: 'flex-start', label: 'Flex-Start' },
  { value: 'flex-end', label: 'Flex-End' },
  { value: 'center', label: 'Center' },
  { value: 'space-between', label: 'Space-Between' },
  { value: 'space-around', label: 'Space-Around' },
  { value: 'space-evenly', label: 'Space-Evenly' },
  { value: 'initial', label: 'Initial' },
  { value: 'inherit', label: 'Inherit' },
];

const whiteSpaces = [
  { value: 'normal', label: 'Normal' },
  { value: 'nowrap', label: 'Nowrap' },
  { value: 'pre', label: 'Pre' },
  { value: 'pre-line', label: 'Pre-line' },
  { value: 'pre-wrap', label: 'Pre-wrap' },
  { value: 'initial', label: 'Initial' },
  { value: 'inherit', label: 'Inherit' },
];

const textOverflows = [
  { value: 'clip', label: 'Clip' },
  { value: 'ellipsis', label: 'Ellipsis' },
  { value: 'string', label: 'String' },
  { value: 'initial', label: 'Initial' },
  { value: 'inherit', label: 'Inherit' },
];

const overflows = [
  { value: 'visible', label: 'Visible' },
  { value: 'hidden', label: 'Hidden' },
  { value: 'clip', label: 'Clip' },
  { value: 'scroll', label: 'Scroll' },
  { value: 'auto', label: 'Auto' },
  { value: 'initial', label: 'Initial' },
  { value: 'inherit', label: 'Inherit' },
];

const typeChartLines = [
  { value: 'monotone', label: 'Monotone' },
  { value: 'basis', label: 'Basis' },
  { value: 'bump', label: 'Bump' },
  { value: 'natural', label: 'Natural' },
  { value: 'step', label: 'Step' },
];

const modalInputTypes = [
  { value: 'multiSelect', label: 'Multiple Select' },
  { value: 'range', label: 'Range' },
  { value: 'radio', label: 'Radio' },
];

const defaultInput = {
  type: modalInputTypes[0],
  label: '',
  contextId: '',
  options: '',
  required: true,
  minSelections: '',
  defaultValue: '',
  min: '',
  max: '',
  radioOptions: [
    {
      label: '',
      value: '',
    },
  ],
  formatType: '',
  formatConfig: {
    maxValue: '',
    maxLabel: '',
    separator: '',
  },
  labels: {
    min: '',
    max: '',
  },
};

const defaultDropdown = {
  label: '',
  value: '',
  contextId: '',
  contextLabel: '',
};

const defaultAction = {
  actionType: actionTypes[0],
  message: '',
  inputType: null,
  screen: null,
  contextId: '',
  contextLabel: '',
  dropdowns: [defaultDropdown],
  title: '',
  description: '',
  inputs: [defaultInput],
};

export default function SettingModal({
  open,
  onClose,
  onOk,
  screens: listScreens,
  element,
}: SettingModalProps) {
  const screenBgColorRef = useRef(null);
  const actionBgColorRef = useRef(null);
  const actionColorRef = useRef(null);
  const actionBorderColorRef = useRef(null);
  const chartStrokeColorRef = useRef(null);

  const screens =
    listScreens?.map((s) => ({
      value: s._id,
      label: s.name || s._id,
      icon: <img src={s.image} alt={s.name} className="h-8 w-10" />,
    })) || [];

  const dataActions = element?.actions?.map((action) => {
    const actionType = actionTypes.find((a) => a.value === action.type) || actionTypes[0];
    return {
      actionType: actionType,
      message: action.message || '',
      contextId: action.dataContextId,
      title: action.modalConfig?.title || '',
      description: action.modalConfig?.description || '',
      contextLabel: action.dataContextLabel || '',
      dropdowns: action.dropdowns?.map((d) => ({
        label: d.label || '',
        value: d.value || '',
        contextId: d.dataContextId || '',
        contextLabel: d.dataContextLabel || '',
      })) || [defaultDropdown],
      inputs: action.modalConfig?.inputs?.map((input) => {
        const modalInputType =
          modalInputTypes.find((i) => i.value === input.type) || modalInputTypes[0];
        return {
          type: modalInputType,
          label: input.label || '',
          contextId: input.dataContextId || '',
          options: input.options?.join(', ') || '',
          required: input.required || true,
          minSelections: input.minSelections?.toString() || '',
          defaultValue: input.defaultValue?.join(', ') || '',
          min: input.min?.toString() || '',
          max: input.max?.toString() || '',
          radioOptions: input.radioOptions,
          formatType: input.formatType || '',
          formatConfig: input.formatConfig,
          labels: input.labels,
        };
      }),
    };
  });

  const defaultPlaceholderType =
    placeholderTypes.find((p) => p.value === element?.type) || placeholderTypes[0];
  const defaultFontWeight =
    fontWeights.find((fw) => fw.value === element?.style?.fontWeight) || fontWeights[0];
  const defaultAlignItems =
    alignItems.find((a) => a.value === element?.style?.alignItems) || alignItems[0];
  const defaultJustifyContent =
    justifyContents.find((j) => j.value === element?.style?.justifyContent) || justifyContents[0];
  const defaultWhiteSpace =
    whiteSpaces.find((w) => w.value === element?.style?.whiteSpace) || whiteSpaces[0];
  const defaultTextOverflow =
    textOverflows.find((t) => t.value === element?.style?.textOverflow) || textOverflows[0];
  const deafultOverflow =
    overflows.find((o) => o.value === element?.style?.overflow) || overflows[0];

  const defaultLines = element?.data?.lines?.map((l) => ({
    name: l.name || '',
    contextId: l.dataContextId || '',
    type: typeChartLines.find((t) => t.value === l.type) || typeChartLines[0],
    stroke: l.stroke || '',
    staticValues: l.staticValues?.join(', ') || '',
  })) || [
    {
      name: '',
      contextId: '',
      type: typeChartLines[0],
      stroke: '',
      staticValues: '',
    },
  ];

  const {
    register,
    handleSubmit,
    setValue,
    getValues,
    control,
    watch,
    formState: { errors },
  } = useForm({
    defaultValues: {
      title: element?.title || '',
      screenBgColor: element?.screenBgColor || '#64767e',
      contentType: element?.contentType || contentTypes[0],
      actionBgColor: element?.backgroundColor || element?.style?.backgroundColor || '',
      actionColor: element?.color || element?.style?.color || '',
      actionBorderColor: element?.border || element?.style?.border || '',
      actions: dataActions || [defaultAction],
      contextId: element?.dataContextId || '',
      placeholderType: defaultPlaceholderType,
      fontSize: element?.style?.fontSize || '',
      fontWeight: defaultFontWeight,
      alignItems: defaultAlignItems,
      justifyContent: defaultJustifyContent,
      whiteSpace: defaultWhiteSpace,
      textOverflow: defaultTextOverflow,
      overflow: deafultOverflow,
      dataByTime: element?.dataByTime || [],
      labelX: element?.data?.labelX || '',
      labelY: element?.data?.labelY || '',
      timePoints: element?.data?.timePoints?.join(', ') || '',
      xLabels: element?.data?.xLabels?.join(', ') || '',
      lines: defaultLines,
    },
  });

  const {
    fields: actionFields,
    append,
    remove,
  } = useFieldArray({
    control,
    name: 'actions',
  });

  const watchContentType = watch('contentType');
  const watchDataByTime = watch('dataByTime');
  const watchLines = watch('lines');

  const onSubmit = (data: any) => {
    if (typeof onOk === 'function') {
      onOk({ ...data, _id: element?._id }, element ? 'update' : 'add');
    }
    onClose();
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <OGDialogTemplate
        className="max-h-[80vh] w-full max-w-xl overflow-y-auto"
        title="Setting"
        main={
          <div className="flex flex-col gap-4 dark:text-white">
            <div>
              <Input
                type="text"
                placeholder="Enter title"
                className="w-full border border-[#424242]"
                {...register('title', {
                  required: 'Title is required',
                  maxLength: {
                    value: 100,
                    message: 'Title cannot exceed 100 characters',
                  },
                })}
              />
              {errors?.title && (
                <span className="text-xs text-red-500">{errors?.title?.message}</span>
              )}
            </div>

            <div>
              <Controller
                name="contentType"
                control={control}
                rules={{ required: 'Content type is required' }}
                defaultValue={contentTypes[0]}
                render={({ field }) => (
                  <SelectDropDown
                    value={field.value}
                    setValue={(data) => field.onChange(data)}
                    availableValues={contentTypes}
                    showLabel={false}
                    showAbove={false}
                    emptyTitle={true}
                  />
                )}
              />
              {errors?.contentType && (
                <span className="text-xs text-red-500">{errors?.contentType?.message}</span>
              )}
            </div>

            <div>
              <div>Screen:</div>
              <div className="relative inline-block">
                <div className="flex items-center gap-2">
                  <div>Background color</div>
                  <div
                    className="h-6 w-12 cursor-pointer rounded border border-gray-300"
                    style={{ backgroundColor: watch(`screenBgColor`) }}
                    onClick={() => screenBgColorRef?.current?.click()}
                  />
                </div>

                <Controller
                  name={`screenBgColor`}
                  control={control}
                  rules={{ required: false }}
                  render={({ field }) => (
                    <input
                      type="color"
                      value={field.value}
                      onChange={(e) => {
                        field.onChange(e.target.value);
                      }}
                      className="absolute left-0 top-0 h-full w-full cursor-pointer opacity-0"
                      ref={screenBgColorRef}
                    />
                  )}
                />
              </div>
            </div>

            {['elements', 'placeholders'].includes(watchContentType?.value) && (
              <div>
                <div>{watchContentType?.label}:</div>
                <div className="flex flex-wrap gap-3">
                  <div className="relative inline-block">
                    <div className="flex items-center gap-2">
                      <div>Background color</div>
                      <div
                        className="h-6 w-12 cursor-pointer rounded border border-gray-300"
                        style={{ backgroundColor: watch(`actionBgColor`) }}
                        onClick={() => actionBgColorRef?.current?.click()}
                      />
                    </div>

                    <Controller
                      name={`actionBgColor`}
                      control={control}
                      rules={{ required: false }}
                      render={({ field }) => (
                        <input
                          type="color"
                          value={field.value}
                          onChange={(e) => {
                            field.onChange(e.target.value);
                          }}
                          className="absolute left-0 top-0 h-full w-full cursor-pointer opacity-0"
                          ref={actionBgColorRef}
                        />
                      )}
                    />
                  </div>
                  {watchContentType?.value === 'placeholders' && (
                    <div className="relative inline-block">
                      <div className="flex items-center gap-2">
                        <div>Text color</div>
                        <div
                          className="h-6 w-12 cursor-pointer rounded border border-gray-300"
                          style={{ backgroundColor: watch(`actionColor`) }}
                          onClick={() => actionColorRef?.current?.click()}
                        />
                      </div>

                      <Controller
                        name={`actionColor`}
                        control={control}
                        rules={{ required: false }}
                        render={({ field }) => (
                          <input
                            type="color"
                            value={field.value}
                            onChange={(e) => {
                              field.onChange(e.target.value);
                            }}
                            className="absolute left-0 top-0 h-full w-full cursor-pointer opacity-0"
                            ref={actionColorRef}
                          />
                        )}
                      />
                    </div>
                  )}
                  <div className="relative inline-block">
                    <div className="flex items-center gap-2">
                      <div>Border color</div>
                      <div
                        className="h-6 w-12 cursor-pointer rounded border border-gray-300"
                        style={{
                          backgroundColor: watch(`actionBorderColor`),
                        }}
                        onClick={() => actionBorderColorRef?.current?.click()}
                      />
                    </div>

                    <Controller
                      name={`actionBorderColor`}
                      control={control}
                      rules={{ required: false }}
                      render={({ field }) => (
                        <input
                          type="color"
                          value={field.value}
                          onChange={(e) => {
                            field.onChange(e.target.value);
                          }}
                          className="absolute left-0 top-0 h-full w-full cursor-pointer opacity-0"
                          ref={actionBorderColorRef}
                        />
                      )}
                    />
                  </div>
                </div>
              </div>
            )}

            {watchContentType?.value === 'elements' ? (
              <div>
                <div className="flex items-center justify-between">
                  <div>Action:</div>
                  <button
                    type="button"
                    className="flex items-center justify-center rounded-md px-2 py-1 hover:bg-red-600/20"
                    onClick={() => {
                      append(defaultAction);
                    }}
                  >
                    <span>Add Action</span>
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="16"
                      height="16"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    >
                      <line x1="12" y1="5" x2="12" y2="19"></line>
                      <line x1="5" y1="12" x2="19" y2="12"></line>
                    </svg>
                  </button>
                </div>

                <div className="mt-1 flex flex-col gap-4">
                  {actionFields?.map((_, index) => (
                    <div
                      className="relative flex flex-col gap-4 rounded-md border border-[#424242] p-4"
                      key={index}
                    >
                      <div className="absolute right-[-12px] top-[-12px]">
                        <button
                          type="button"
                          className="flex h-8 w-8 items-center justify-center rounded-full hover:bg-red-600/20"
                          disabled={actionFields.length <= 1}
                          onClick={() => {
                            if (actionFields.length > 1) {
                              remove(index);
                            }
                          }}
                        >
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="16"
                            height="16"
                            viewBox="0 0 24 24"
                            fill="none"
                            stroke="currentColor"
                            strokeWidth="2"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          >
                            <line x1="18" y1="6" x2="6" y2="18"></line>
                            <line x1="6" y1="6" x2="18" y2="18"></line>
                          </svg>
                        </button>
                      </div>

                      <div>
                        <Controller
                          name={`actions[${index}].actionType`}
                          control={control}
                          rules={{ required: 'Action type is required' }}
                          defaultValue={actionTypes[0]}
                          render={({ field }) => (
                            <SelectDropDown
                              value={field.value}
                              setValue={(data) => field.onChange(data)}
                              availableValues={actionTypes}
                              showLabel={false}
                              showAbove={false}
                              emptyTitle={true}
                            />
                          )}
                        />
                        {errors?.actions?.[index]?.actionType && (
                          <span className="text-xs text-red-500">
                            {errors?.actions?.[index]?.actionType?.message}
                          </span>
                        )}
                      </div>

                      {watch(`actions[${index}].actionType`)?.value !== 'modal' && (
                        <>
                          <Input
                            type="text"
                            placeholder="Enter message"
                            className="w-full border-[#424242]"
                            {...register(`actions[${index}].message`, {
                              required: false,
                            })}
                          />

                          <Controller
                            name={`actions[${index}].inputType`}
                            control={control}
                            rules={{ required: false }}
                            defaultValue={inputTypes[0]}
                            render={({ field }) => (
                              <SelectDropDown
                                value={field.value}
                                setValue={(data) => field.onChange(data)}
                                availableValues={inputTypes}
                                showLabel={false}
                                showAbove={false}
                                emptyTitle={true}
                                placeholder="Select input type"
                              />
                            )}
                          />

                          <Controller
                            name={`actions[${index}].screen`}
                            control={control}
                            rules={{ required: false }}
                            defaultValue={screens[0]}
                            render={({ field }) => (
                              <SelectDropDown
                                value={field.value}
                                setValue={(data) => field.onChange(data)}
                                availableValues={screens}
                                showLabel={false}
                                showAbove={false}
                                emptyTitle={true}
                                placeholder="Select screen"
                              />
                            )}
                          />

                          <Input
                            type="text"
                            placeholder="Enter context ID"
                            className="w-full border-[#424242]"
                            {...register(`actions[${index}].contextId`, {
                              required: false,
                            })}
                          />

                          <Input
                            type="text"
                            placeholder="Enter context label"
                            className="w-full border-[#424242]"
                            {...register(`actions[${index}].contextLabel`, {
                              required: false,
                            })}
                          />
                        </>
                      )}

                      {watch(`actions[${index}].actionType`)?.value === 'dropdown' && (
                        <div>
                          <div className="mb-1 flex items-center justify-between">
                            <div>Dropdown Option:</div>
                            <button
                              type="button"
                              className="flex items-center justify-center rounded-md px-2 py-1 hover:bg-red-600/20"
                              onClick={() => {
                                const currentDropdown =
                                  getValues(`actions[${index}].dropdowns`) || [];

                                setValue(`actions[${index}].dropdowns`, [
                                  ...currentDropdown,
                                  defaultDropdown,
                                ]);
                              }}
                            >
                              <span>Add Option</span>
                              <svg
                                xmlns="http://www.w3.org/2000/svg"
                                width="16"
                                height="16"
                                viewBox="0 0 24 24"
                                fill="none"
                                stroke="currentColor"
                                strokeWidth="2"
                                strokeLinecap="round"
                                strokeLinejoin="round"
                              >
                                <line x1="12" y1="5" x2="12" y2="19"></line>
                                <line x1="5" y1="12" x2="19" y2="12"></line>
                              </svg>
                            </button>
                          </div>

                          <div
                            className={`flex flex-col gap-4 ${watch(`actions[${index}].dropdowns`)?.length > 0 ? 'rounded-md border border-[#424242] p-4' : ''} `}
                          >
                            {watch(`actions[${index}].dropdowns`)?.map((_, idx) => (
                              <div key={`dropdowns_${idx}`} className="flex justify-between gap-4">
                                <div className="flex flex-1 flex-col gap-2">
                                  <div className="flex gap-4">
                                    <div className="flex-1">
                                      <Input
                                        type="text"
                                        placeholder="Enter name"
                                        className="w-full border-[#424242]"
                                        {...register(`actions.${index}.dropdowns.${idx}.label`, {
                                          required:
                                            'Name is required and cannot exceed 50 characters',
                                          maxLength: 50,
                                        })}
                                      />
                                      {errors?.actions?.[index].dropdowns?.[idx]?.label && (
                                        <span className="text-xs text-red-500">
                                          {errors.actions[index].dropdowns[idx].label.message}
                                        </span>
                                      )}
                                    </div>
                                    <div className="flex-1">
                                      <Input
                                        type="text"
                                        placeholder="Enter value"
                                        className="w-full border-[#424242]"
                                        {...register(`actions.${index}.dropdowns.${idx}.value`, {
                                          required:
                                            'Value is required and cannot exceed 50 characters',
                                          maxLength: 50,
                                        })}
                                      />
                                      {errors?.actions?.[index].dropdowns?.[idx]?.value && (
                                        <span className="text-xs text-red-500">
                                          {errors.actions[index].dropdowns[idx].value.message}
                                        </span>
                                      )}
                                    </div>
                                  </div>

                                  <div className="flex gap-4">
                                    <div className="flex-1">
                                      <Input
                                        type="text"
                                        placeholder="Enter context ID"
                                        className="w-full border-[#424242]"
                                        {...register(
                                          `actions.${index}.dropdowns.${idx}.contextId`,
                                          {
                                            required:
                                              'contextId is required and cannot exceed 50 characters',
                                            maxLength: 50,
                                          },
                                        )}
                                      />
                                      {errors?.actions?.[index].dropdowns?.[idx]?.contextId && (
                                        <span className="text-xs text-red-500">
                                          {errors.actions[index].dropdowns[idx].contextId.message}
                                        </span>
                                      )}
                                    </div>
                                    <div className="flex-1">
                                      <Input
                                        type="text"
                                        placeholder="Enter context label"
                                        className="w-full border-[#424242]"
                                        {...register(
                                          `actions.${index}.dropdowns.${idx}.contextLabel`,
                                          {
                                            required:
                                              'contextLabel is required and cannot exceed 50 characters',
                                            maxLength: 50,
                                          },
                                        )}
                                      />
                                      {errors?.actions?.[index].dropdowns?.[idx]?.contextLabel && (
                                        <span className="text-xs text-red-500">
                                          {
                                            errors.actions[index].dropdowns[idx].contextLabel
                                              .message
                                          }
                                        </span>
                                      )}
                                    </div>
                                  </div>
                                </div>

                                {/* Nút xoá bên phải */}
                                <button
                                  type="button"
                                  className="flex h-8 w-8 items-center justify-center self-start rounded-full hover:bg-red-600/20"
                                  onClick={() => {
                                    const currentDropdown = getValues(`actions.${index}.dropdowns`);
                                    setValue(
                                      `actions.${index}.dropdowns`,
                                      currentDropdown.filter((_, i) => i !== idx),
                                    );
                                  }}
                                >
                                  <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    width="16"
                                    height="16"
                                    viewBox="0 0 24 24"
                                    fill="none"
                                    stroke="currentColor"
                                    strokeWidth="2"
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                  >
                                    <line x1="18" y1="6" x2="6" y2="18" />
                                    <line x1="6" y1="6" x2="18" y2="18" />
                                  </svg>
                                </button>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}

                      {watch(`actions[${index}].actionType`)?.value === 'modal' && (
                        <>
                          <Input
                            type="text"
                            placeholder="Enter title"
                            className="w-full border-[#424242]"
                            {...register(`actions[${index}].title`, {
                              required: false,
                            })}
                          />

                          <Input
                            type="text"
                            placeholder="Enter description"
                            className="w-full border-[#424242]"
                            {...register(`actions[${index}].description`, {
                              required: false,
                            })}
                          />

                          <div>
                            <div className="mb-1 flex items-center justify-between">
                              <div>Modal Option:</div>
                              <button
                                type="button"
                                className="flex items-center justify-center rounded-md px-2 py-1 hover:bg-red-600/20"
                                onClick={() => {
                                  const currentInputs = getValues(`actions[${index}].inputs`) || [];

                                  setValue(`actions[${index}].inputs`, [
                                    ...currentInputs,
                                    defaultInput,
                                  ]);
                                }}
                              >
                                <span>Add Option</span>
                                <svg
                                  xmlns="http://www.w3.org/2000/svg"
                                  width="16"
                                  height="16"
                                  viewBox="0 0 24 24"
                                  fill="none"
                                  stroke="currentColor"
                                  strokeWidth="2"
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                >
                                  <line x1="12" y1="5" x2="12" y2="19"></line>
                                  <line x1="5" y1="12" x2="19" y2="12"></line>
                                </svg>
                              </button>
                            </div>

                            <div
                              className={`flex flex-col gap-4 ${watch(`actions[${index}].inputs`)?.length > 0 ? 'rounded-md border border-[#424242] p-4' : ''} `}
                            >
                              {watch(`actions[${index}].inputs`)?.map((_, idx) => (
                                <div key={`inputs_${idx}`} className="flex justify-between gap-4">
                                  <div className="flex flex-1 flex-col gap-2">
                                    <div>
                                      <div className="text-sm text-gray-400">Input type:</div>
                                      <Controller
                                        name={`actions.${index}.inputs.${idx}.type`}
                                        control={control}
                                        rules={{ required: false }}
                                        defaultValue={modalInputTypes[0]}
                                        render={({ field }) => (
                                          <SelectDropDown
                                            value={field.value}
                                            setValue={(data) => field.onChange(data)}
                                            availableValues={modalInputTypes}
                                            showLabel={false}
                                            showAbove={false}
                                            emptyTitle={true}
                                            placeholder="Select input type"
                                          />
                                        )}
                                      />
                                    </div>

                                    <div className="flex gap-4">
                                      <div className="flex-1">
                                        <Input
                                          type="text"
                                          placeholder="Enter label"
                                          className="w-full border-[#424242]"
                                          {...register(`actions.${index}.inputs.${idx}.label`, {
                                            required: false,
                                          })}
                                        />
                                      </div>
                                      <div className="flex-1">
                                        <Input
                                          type="text"
                                          placeholder="Enter Context ID"
                                          className="w-full border-[#424242]"
                                          {...register(`actions.${index}.inputs.${idx}.contextId`, {
                                            required: false,
                                          })}
                                        />
                                      </div>
                                    </div>

                                    <div>
                                      <Input
                                        type="text"
                                        placeholder="Enter options (comma separated)"
                                        className="w-full border-[#424242]"
                                        {...register(`actions.${index}.inputs.${idx}.options`, {
                                          required: false,
                                        })}
                                      />
                                    </div>

                                    <div className="flex gap-4">
                                      <div className="flex-1">
                                        <Input
                                          type="text"
                                          placeholder="Enter min selection"
                                          className="w-full border-[#424242]"
                                          {...register(
                                            `actions.${index}.inputs.${idx}.minSelections`,
                                            {
                                              required: false,
                                            },
                                          )}
                                        />
                                      </div>
                                      <div className="flex flex-1 items-center gap-2">
                                        <Controller
                                          name={`actions.${index}.inputs.${idx}.required`}
                                          control={control}
                                          render={({ field }) => (
                                            <Switch
                                              {...field}
                                              checked={field.value}
                                              onCheckedChange={field.onChange}
                                              className="data-[state=checked]:bg-gray-400"
                                            />
                                          )}
                                        />
                                        <p className="text-base font-normal">Required data</p>
                                      </div>
                                    </div>

                                    <div>
                                      <Input
                                        type="text"
                                        placeholder="Enter default value (comma separated)"
                                        className="w-full border-[#424242]"
                                        {...register(
                                          `actions.${index}.inputs.${idx}.defaultValue`,
                                          {
                                            required: false,
                                          },
                                        )}
                                      />
                                    </div>

                                    <div className="flex gap-4">
                                      <div className="flex-1">
                                        <Input
                                          type="text"
                                          placeholder="Enter min"
                                          className="w-full border-[#424242]"
                                          {...register(`actions.${index}.inputs.${idx}.min`, {
                                            required: false,
                                          })}
                                        />
                                      </div>
                                      <div className="flex-1">
                                        <Input
                                          type="text"
                                          placeholder="Enter max"
                                          className="w-full border-[#424242]"
                                          {...register(`actions.${index}.inputs.${idx}.max`, {
                                            required: false,
                                          })}
                                        />
                                      </div>
                                    </div>

                                    <div>
                                      <div className="mb-1 flex items-center justify-between">
                                        <div>Radio Option:</div>
                                        <button
                                          type="button"
                                          className="flex items-center justify-center rounded-md px-2 py-1 hover:bg-red-600/20"
                                          onClick={() => {
                                            const currentRadioOptions =
                                              getValues(
                                                `actions.${index}.inputs.${idx}.radioOptions`,
                                              ) || [];

                                            setValue(
                                              `actions.${index}.inputs.${idx}.radioOptions`,
                                              [...currentRadioOptions, { label: '', value: '' }],
                                            );
                                          }}
                                        >
                                          <span>Add Option</span>
                                          <svg
                                            xmlns="http://www.w3.org/2000/svg"
                                            width="16"
                                            height="16"
                                            viewBox="0 0 24 24"
                                            fill="none"
                                            stroke="currentColor"
                                            strokeWidth="2"
                                            strokeLinecap="round"
                                            strokeLinejoin="round"
                                          >
                                            <line x1="12" y1="5" x2="12" y2="19"></line>
                                            <line x1="5" y1="12" x2="19" y2="12"></line>
                                          </svg>
                                        </button>
                                      </div>

                                      <div
                                        className={`flex flex-col gap-4 ${watch(`actions.${index}.inputs.${idx}.radioOptions`)?.length > 0 ? 'rounded-md border border-[#424242] p-4' : ''} `}
                                      >
                                        {watch(`actions.${index}.inputs.${idx}.radioOptions`)?.map(
                                          (_, rIndex) => (
                                            <div
                                              key={`radioOptions_${rIndex}`}
                                              className="flex justify-between gap-4"
                                            >
                                              <div className="flex flex-1 flex-col gap-2">
                                                <div className="flex gap-4">
                                                  <div className="flex-1">
                                                    <Input
                                                      type="text"
                                                      placeholder="Enter label"
                                                      className="w-full border-[#424242]"
                                                      {...register(
                                                        `actions.${index}.inputs.${idx}.radioOptions.${rIndex}.label`,
                                                        {
                                                          required: false,
                                                        },
                                                      )}
                                                    />
                                                  </div>
                                                  <div className="flex-1">
                                                    <Input
                                                      type="text"
                                                      placeholder="Enter value"
                                                      className="w-full border-[#424242]"
                                                      {...register(
                                                        `actions.${index}.inputs.${idx}.radioOptions.${rIndex}.value`,
                                                        {
                                                          required: false,
                                                        },
                                                      )}
                                                    />
                                                  </div>
                                                </div>
                                              </div>

                                              {/* Nút xoá bên phải */}
                                              <button
                                                type="button"
                                                className="flex h-8 w-8 items-center justify-center self-start rounded-full hover:bg-red-600/20"
                                                onClick={() => {
                                                  const currentDropdown = getValues(
                                                    `actions.${index}.inputs.${idx}.radioOptions`,
                                                  );
                                                  setValue(
                                                    `actions.${index}.inputs.${idx}.radioOptions`,
                                                    currentDropdown.filter((_, i) => i !== idx),
                                                  );
                                                }}
                                              >
                                                <svg
                                                  xmlns="http://www.w3.org/2000/svg"
                                                  width="16"
                                                  height="16"
                                                  viewBox="0 0 24 24"
                                                  fill="none"
                                                  stroke="currentColor"
                                                  strokeWidth="2"
                                                  strokeLinecap="round"
                                                  strokeLinejoin="round"
                                                >
                                                  <line x1="18" y1="6" x2="6" y2="18" />
                                                  <line x1="6" y1="6" x2="18" y2="18" />
                                                </svg>
                                              </button>
                                            </div>
                                          ),
                                        )}
                                      </div>
                                    </div>

                                    <div>
                                      <Input
                                        type="text"
                                        placeholder="Enter format type"
                                        className="w-full border-[#424242]"
                                        {...register(`actions.${index}.inputs.${idx}.formatType`, {
                                          required: false,
                                        })}
                                      />
                                    </div>

                                    <div>
                                      <div className="text-sm text-gray-400">Format config:</div>
                                      <div className="flex gap-4">
                                        <div className="flex-1">
                                          <Input
                                            type="text"
                                            placeholder="Enter max label"
                                            className="w-full border-[#424242]"
                                            {...register(
                                              `actions.${index}.inputs.${idx}.formatConfig.maxLabel`,
                                              {
                                                required: false,
                                              },
                                            )}
                                          />
                                        </div>
                                        <div className="flex-1">
                                          <Input
                                            type="text"
                                            placeholder="Enter max value"
                                            className="w-full border-[#424242]"
                                            {...register(
                                              `actions.${index}.inputs.${idx}.formatConfig.maxValue`,
                                              {
                                                required: false,
                                              },
                                            )}
                                          />
                                        </div>
                                        <div className="flex-1">
                                          <Input
                                            type="text"
                                            placeholder="Enter separator"
                                            className="w-full border-[#424242]"
                                            {...register(
                                              `actions.${index}.inputs.${idx}.formatConfig.separator`,
                                              {
                                                required: false,
                                              },
                                            )}
                                          />
                                        </div>
                                      </div>
                                    </div>

                                    <div>
                                      <div className="text-sm text-gray-400">Labels:</div>
                                      <div className="flex gap-4">
                                        <div className="flex-1">
                                          <Input
                                            type="text"
                                            placeholder="Enter min label"
                                            className="w-full border-[#424242]"
                                            {...register(
                                              `actions.${index}.inputs.${idx}.labels.min`,
                                              {
                                                required: false,
                                              },
                                            )}
                                          />
                                        </div>
                                        <div className="flex-1">
                                          <Input
                                            type="text"
                                            placeholder="Enter max label"
                                            className="w-full border-[#424242]"
                                            {...register(
                                              `actions.${index}.inputs.${idx}.labels.max`,
                                              {
                                                required: false,
                                              },
                                            )}
                                          />
                                        </div>
                                      </div>
                                    </div>
                                  </div>

                                  {/* Nút xoá bên phải */}
                                  <button
                                    type="button"
                                    className="flex h-8 w-8 items-center justify-center self-start rounded-full hover:bg-red-600/20"
                                    onClick={() => {
                                      const currentInputs = getValues(`actions.${index}.inputs`);
                                      setValue(
                                        `actions.${index}.inputs`,
                                        currentInputs.filter((_, i) => i !== idx),
                                      );
                                    }}
                                  >
                                    <svg
                                      xmlns="http://www.w3.org/2000/svg"
                                      width="16"
                                      height="16"
                                      viewBox="0 0 24 24"
                                      fill="none"
                                      stroke="currentColor"
                                      strokeWidth="2"
                                      strokeLinecap="round"
                                      strokeLinejoin="round"
                                    >
                                      <line x1="18" y1="6" x2="6" y2="18" />
                                      <line x1="6" y1="6" x2="18" y2="18" />
                                    </svg>
                                  </button>
                                </div>
                              ))}
                            </div>
                          </div>
                        </>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            ) : watchContentType?.value === 'placeholders' ? (
              <div className="flex flex-col gap-4 dark:text-white">
                <div className="grid grid-cols-3 gap-4">
                  <Input
                    type="text"
                    placeholder="Enter font size"
                    className="w-full border-[#424242]"
                    {...register(`fontSize`, {
                      required: false,
                    })}
                  />

                  <Controller
                    name={`fontWeight`}
                    control={control}
                    render={({ field }) => (
                      <SelectDropDown
                        value={field.value}
                        setValue={(data) => field.onChange(data)}
                        availableValues={fontWeights}
                        showLabel={false}
                        showAbove={false}
                        emptyTitle={true}
                        placeholder="Select font weight"
                        className="w-full"
                      />
                    )}
                  />

                  <Controller
                    name={`alignItems`}
                    control={control}
                    render={({ field }) => (
                      <SelectDropDown
                        value={field.value}
                        setValue={(data) => field.onChange(data)}
                        availableValues={alignItems}
                        showLabel={false}
                        showAbove={false}
                        emptyTitle={true}
                        placeholder="Select align items"
                        className="w-full"
                      />
                    )}
                  />

                  <Controller
                    name={`justifyContent`}
                    control={control}
                    render={({ field }) => (
                      <SelectDropDown
                        value={field.value}
                        setValue={(data) => field.onChange(data)}
                        availableValues={justifyContents}
                        showLabel={false}
                        showAbove={false}
                        emptyTitle={true}
                        placeholder="Select justify content"
                        className="w-full"
                      />
                    )}
                  />

                  <Controller
                    name={`whiteSpace`}
                    control={control}
                    render={({ field }) => (
                      <SelectDropDown
                        value={field.value}
                        setValue={(data) => field.onChange(data)}
                        availableValues={whiteSpaces}
                        showLabel={false}
                        showAbove={false}
                        emptyTitle={true}
                        placeholder="Select white space"
                        className="w-full"
                      />
                    )}
                  />

                  <Controller
                    name={`textOverflow`}
                    control={control}
                    render={({ field }) => (
                      <SelectDropDown
                        value={field.value}
                        setValue={(data) => field.onChange(data)}
                        availableValues={textOverflows}
                        showLabel={false}
                        showAbove={false}
                        emptyTitle={true}
                        placeholder="Select text overflow"
                        className="w-full"
                      />
                    )}
                  />

                  <Controller
                    name={`overflow`}
                    control={control}
                    render={({ field }) => (
                      <SelectDropDown
                        value={field.value}
                        setValue={(data) => field.onChange(data)}
                        availableValues={overflows}
                        showLabel={false}
                        showAbove={false}
                        emptyTitle={true}
                        placeholder="Select text overflow"
                        className="w-full"
                      />
                    )}
                  />
                </div>

                <div>
                  <Controller
                    name={`placeholderType`}
                    control={control}
                    rules={{ required: 'Placeholder type is required' }}
                    defaultValue={placeholderTypes[0]}
                    render={({ field }) => (
                      <SelectDropDown
                        value={field.value}
                        setValue={(data) => field.onChange(data)}
                        availableValues={placeholderTypes}
                        showLabel={false}
                        showAbove={false}
                        emptyTitle={true}
                      />
                    )}
                  />
                  {errors?.placeholderType && (
                    <span className="text-xs text-red-500">{errors?.placeholderType?.message}</span>
                  )}
                </div>

                <Input
                  type="text"
                  placeholder="Enter context ID"
                  className="w-full border-[#424242]"
                  {...register(`contextId`, {
                    required: false,
                  })}
                />

                <div>
                  <div className="mb-1 flex items-center justify-between">
                    <div>Data by time:</div>
                    <button
                      type="button"
                      className="flex items-center justify-center rounded-md px-2 py-1 hover:bg-red-600/20"
                      onClick={() => {
                        const currentData = getValues(`dataByTime`) || [];
                        setValue(`dataByTime`, [...currentData, { name: '', value: '' }]);
                      }}
                    >
                      <span>Add Option</span>
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="16"
                        height="16"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      >
                        <line x1="12" y1="5" x2="12" y2="19"></line>
                        <line x1="5" y1="12" x2="19" y2="12"></line>
                      </svg>
                    </button>
                  </div>

                  <div
                    className={`flex flex-col gap-4 ${watchDataByTime?.length > 0 ? 'rounded-md border border-[#424242] p-4' : ''} `}
                  >
                    {watchDataByTime?.map((_, idx) => (
                      <div key={`dataByTime_${idx}`} className="flex justify-between gap-4">
                        <div className="flex flex-1 flex-col gap-2">
                          <div className="flex gap-4">
                            <div className="flex-1">
                              <Input
                                type="text"
                                placeholder="Enter name"
                                className="w-full border-[#424242]"
                                {...register(`dataByTime.${idx}.name`, {
                                  required: 'Name is required and cannot exceed 50 characters',
                                  maxLength: 50,
                                })}
                              />
                              {errors?.dataByTime?.[idx]?.name && (
                                <span className="text-xs text-red-500">
                                  {errors.dataByTime[idx].name.message}
                                </span>
                              )}
                            </div>
                            <div className="flex-1">
                              <Input
                                type="text"
                                placeholder="Enter value"
                                className="w-full border-[#424242]"
                                {...register(`dataByTime.${idx}.value`, {
                                  required: 'Value is required and cannot exceed 50 characters',
                                  maxLength: 50,
                                })}
                              />
                              {errors?.dataByTime?.[idx]?.value && (
                                <span className="text-xs text-red-500">
                                  {errors.dataByTime[idx].value.message}
                                </span>
                              )}
                            </div>
                          </div>
                        </div>

                        <button
                          type="button"
                          className="flex h-8 w-8 items-center justify-center self-start rounded-full hover:bg-red-600/20"
                          onClick={() => {
                            const currentDropdown = getValues(`dataByTime`);
                            setValue(
                              `dataByTime`,
                              currentDropdown.filter((_, i) => i !== idx),
                            );
                          }}
                        >
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="16"
                            height="16"
                            viewBox="0 0 24 24"
                            fill="none"
                            stroke="currentColor"
                            strokeWidth="2"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          >
                            <line x1="18" y1="6" x2="6" y2="18" />
                            <line x1="6" y1="6" x2="18" y2="18" />
                          </svg>
                        </button>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            ) : watchContentType?.value === 'charts' ? (
              <div className="flex flex-col gap-4 dark:text-white">
                <div className="grid grid-cols-2 gap-4">
                  <Input
                    type="text"
                    placeholder="Enter label X"
                    className="w-full border-[#424242]"
                    {...register(`labelX`, {
                      required: false,
                    })}
                  />

                  <Input
                    type="text"
                    placeholder="Enter label Y"
                    className="w-full border-[#424242]"
                    {...register(`labelY`, {
                      required: false,
                    })}
                  />
                </div>

                <Input
                  type="text"
                  placeholder="Enter time points (comma separated)"
                  className="w-full border-[#424242]"
                  {...register(`timePoints`, {
                    required: false,
                  })}
                />

                <Input
                  type="text"
                  placeholder="Enter X labels (comma separated)"
                  className="w-full border-[#424242]"
                  {...register(`xLabels`, {
                    required: false,
                  })}
                />

                <div>
                  <div className="mb-1 flex items-center justify-between">
                    <div>Lines:</div>
                    <button
                      type="button"
                      className="flex items-center justify-center rounded-md px-2 py-1 hover:bg-red-600/20"
                      onClick={() => {
                        const currentData = getValues(`lines`) || [];
                        setValue(`lines`, [
                          ...currentData,
                          {
                            name: '',
                            contextId: '',
                            type: typeChartLines[0],
                            stroke: '',
                            staticValues: '',
                          },
                        ]);
                      }}
                    >
                      <span>Add Option</span>
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="16"
                        height="16"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      >
                        <line x1="12" y1="5" x2="12" y2="19"></line>
                        <line x1="5" y1="12" x2="19" y2="12"></line>
                      </svg>
                    </button>
                  </div>

                  <div
                    className={`flex flex-col gap-4 ${watchLines?.length > 0 ? 'rounded-md border border-[#424242] p-4' : ''} `}
                  >
                    {watchLines?.map((_, idx) => (
                      <div key={`lines_${idx}`} className="flex justify-between gap-4">
                        <div className="flex flex-1 flex-col gap-2">
                          <div className="flex gap-4">
                            <div className="flex-1">
                              <Input
                                type="text"
                                placeholder="Enter name"
                                className="w-full border-[#424242]"
                                {...register(`lines.${idx}.name`, {
                                  required: 'Name is required and cannot exceed 50 characters',
                                  maxLength: 50,
                                })}
                              />
                              {errors?.lines?.[idx]?.name && (
                                <span className="text-xs text-red-500">
                                  {errors.lines[idx].name.message}
                                </span>
                              )}
                            </div>

                            <div className="flex-1">
                              <Input
                                type="text"
                                placeholder="Enter context ID"
                                className="w-full border-[#424242]"
                                {...register(`lines.${idx}.contextId`, {
                                  required: false,
                                })}
                              />
                            </div>
                          </div>

                          <div className="flex items-center gap-4">
                            <div className="flex-1">
                              <Controller
                                name={`lines.${idx}.type`}
                                control={control}
                                rules={{ required: 'Chart type is required' }}
                                defaultValue={typeChartLines[0]}
                                render={({ field }) => (
                                  <SelectDropDown
                                    value={field.value}
                                    setValue={(data) => field.onChange(data)}
                                    availableValues={typeChartLines}
                                    showLabel={false}
                                    showAbove={false}
                                    emptyTitle={true}
                                  />
                                )}
                              />
                              {errors?.placeholderType && (
                                <span className="text-xs text-red-500">
                                  {errors?.placeholderType?.message}
                                </span>
                              )}
                            </div>

                            <div className="relative inline-block flex-1">
                              <div className="flex items-center gap-2">
                                <div>Stroke</div>
                                <div
                                  className="h-6 w-12 cursor-pointer rounded border border-gray-300"
                                  style={{ backgroundColor: watch(`lines.${idx}.stroke`) }}
                                  onClick={() => chartStrokeColorRef?.current?.click()}
                                />
                              </div>

                              <Controller
                                name={`lines.${idx}.stroke`}
                                control={control}
                                rules={{ required: false }}
                                render={({ field }) => (
                                  <input
                                    type="color"
                                    value={field.value}
                                    onChange={(e) => {
                                      field.onChange(e.target.value);
                                    }}
                                    className="absolute left-0 top-0 h-full w-full cursor-pointer opacity-0"
                                    ref={chartStrokeColorRef}
                                  />
                                )}
                              />
                            </div>
                          </div>

                          <div className="">
                            <Input
                              type="text"
                              placeholder="Enter static value (comma separated)"
                              className="w-full border-[#424242]"
                              {...register(`lines.${idx}.staticValues`, {
                                required: false,
                              })}
                            />
                            {errors?.lines?.[idx]?.staticValues && (
                              <span className="text-xs text-red-500">
                                {errors.lines[idx].staticValues.message}
                              </span>
                            )}
                          </div>
                        </div>

                        <button
                          type="button"
                          className="flex h-8 w-8 items-center justify-center self-start rounded-full hover:bg-red-600/20"
                          onClick={() => {
                            const currentLines = getValues(`lines`);
                            setValue(
                              `lines`,
                              currentLines.filter((_, i) => i !== idx),
                            );
                          }}
                        >
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="16"
                            height="16"
                            viewBox="0 0 24 24"
                            fill="none"
                            stroke="currentColor"
                            strokeWidth="2"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          >
                            <line x1="18" y1="6" x2="6" y2="18" />
                            <line x1="6" y1="6" x2="18" y2="18" />
                          </svg>
                        </button>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            ) : (
              ''
            )}
          </div>
        }
        buttons={
          <div>
            <Button onClick={handleSubmit(onSubmit)}>{element ? 'Update' : 'Add'}</Button>
          </div>
        }
      />
    </Dialog>
  );
}
