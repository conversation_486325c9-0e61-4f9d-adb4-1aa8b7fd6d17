import { useEffect, useRef, useState } from 'react';
import { Button, Dialog, OGDialogTemplate } from '~/components/ui';
import { PlusIcon, XIcon } from '~/components/svg';

interface IProps {
  open: boolean;
  onClose: () => void;
  onAdd: (files: File[]) => void;
  maxFiles?: number;
  disabled?: boolean;
}

export default function AddScreensModal({ open, onClose, onAdd, maxFiles = 12, disabled }: IProps) {
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
  const [previewUrls, setPreviewUrls] = useState<string[]>([]);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    if (files.length === 0) return;

    const imageFiles = files.filter((file) => file.type.startsWith('image/'));

    const newFiles = [...selectedFiles, ...imageFiles].slice(0, maxFiles);
    setSelectedFiles(newFiles);

    const newPreviewUrls = newFiles.map((file) => URL.createObjectURL(file));

    previewUrls.forEach((url) => URL.revokeObjectURL(url));

    setPreviewUrls(newPreviewUrls);
  };

  const handleAddClick = () => {
    fileInputRef.current?.click();
  };

  const handleRemoveFile = (index: number) => {
    URL.revokeObjectURL(previewUrls[index]);

    const newFiles = [...selectedFiles];
    newFiles.splice(index, 1);
    setSelectedFiles(newFiles);

    const newUrls = [...previewUrls];
    newUrls.splice(index, 1);
    setPreviewUrls(newUrls);
  };

  const handleSubmit = () => {
    onAdd(selectedFiles);
  };

  useEffect(() => {
    return () => {
      previewUrls.forEach((url) => URL.revokeObjectURL(url));
    };
  }, []);

  useEffect(() => {
    if (!open) {
      setSelectedFiles([]);
      previewUrls.forEach((url) => URL.revokeObjectURL(url));
      setPreviewUrls([]);
    }
  }, [open]);

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <OGDialogTemplate
        className="w-full max-w-4xl"
        title="Add New Screens"
        main={
          <div className="flex flex-col gap-4">
            <div className="mb-2 flex items-center justify-between">
              <p className="text-sm text-gray-400">
                Select up to {maxFiles} images for your screens
              </p>
              <p className="text-sm text-gray-400">
                {selectedFiles.length}/{maxFiles} selected
              </p>
            </div>

            <div className="grid grid-cols-2 gap-4 md:grid-cols-3 lg:grid-cols-4">
              {selectedFiles.length < maxFiles && (
                <div
                  className="flex h-40 w-full cursor-pointer flex-col items-center justify-center rounded-lg border border-dashed border-gray-400 bg-gray-800 hover:border-purple-500"
                  onClick={handleAddClick}
                >
                  <PlusIcon className="mb-2 h-8 w-8 text-gray-400" />
                  <p className="text-center text-xs text-gray-400">Click to select images</p>
                  <p className="mt-1 text-center text-xs text-gray-500">PNG, JPG, JPEG</p>
                </div>
              )}

              {previewUrls.map((url, index) => (
                <div key={index} className="relative h-40 w-full overflow-hidden rounded-lg">
                  <img src={url} alt={`Preview ${index}`} className="h-full w-full object-cover" />
                  <button
                    className="absolute right-2 top-2 flex h-6 w-6 items-center justify-center rounded-full bg-gray-800 p-1 text-gray-300 hover:bg-red-600 hover:text-white"
                    onClick={() => handleRemoveFile(index)}
                    title="Remove image"
                  >
                    <XIcon className="h-4 w-4" />
                  </button>
                  <div className="absolute bottom-0 left-0 right-0 bg-gray-800 bg-opacity-70 px-2 py-1 text-xs text-white">
                    {selectedFiles[index].name}
                  </div>
                </div>
              ))}
            </div>

            <input
              type="file"
              className="hidden"
              ref={fileInputRef}
              accept="image/*"
              multiple
              onChange={handleFileChange}
            />

            {selectedFiles.length === 0 && (
              <div className="mt-4 text-center text-sm text-gray-400">
                No images selected. Click the box above to add images.
              </div>
            )}
          </div>
        }
        buttons={
          <Button onClick={handleSubmit} disabled={selectedFiles.length === 0 || disabled}>
            Add Screens
          </Button>
        }
      />
    </Dialog>
  );
}
