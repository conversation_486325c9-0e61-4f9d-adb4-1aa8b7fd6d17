import React, { useEffect, useRef, useState, memo } from 'react';

interface BrowserStreamProps {
    wsUrl: string;
    userId: string;
    browserUrl?: string;
    onStatusChange?: (status: string) => void;
    onConnectionChange?: (connected: boolean) => void;
}
const maxRetries = 3;

const BrowserStream: React.FC<BrowserStreamProps> = ({ wsUrl, userId, browserUrl }) => {
    const canvasRef = useRef<HTMLCanvasElement>(null);
    // const overlayCanvasRef = useRef<HTMLCanvasElement>(null);
    const wsRef = useRef<WebSocket | null>(null);
    const [status, setStatus] = useState<string>('Connecting...');
    const [statusCode, setStatusCode] = useState<string>('');
    const [connected, setConnected] = useState(false);
    const [showReconnectDialog, setShowReconnectDialog] = useState(false);
    // const [clickOverlays, setClickOverlays] = useState<ClickOverlay[]>([]);
    const [initUrl] = useState<string>(browserUrl || "");
    const [liveViewLink, setLiveViewLink] = useState<string>("");
    const [currentUrl, setCurrentUrl] = useState<string>("");
    const [urlInput, setUrlInput] = useState<string>('');
    const [isEditingUrl, setIsEditingUrl] = useState(false);
    const [canGoBack, _setCanGoBack] = useState(false);
    const [canGoForward, _setCanGoForward] = useState(false);

    // Initialize WebSocket connection
    useEffect(() => {
        // connectWebSocket();
        if (wsRef.current || !wsUrl) return;
        console.log("Connecting to WebSocket... ::: ", wsUrl);
        try {
            wsRef.current = new WebSocket(`${wsUrl}?userId=${userId}`);
            // wsRef.current = new WebSocket(wsUrl);

            wsRef.current.onopen = () => {
                setStatus('Connected to server');
                setConnected(true);
                setShowReconnectDialog(false);
            };

            wsRef.current.onmessage = (event: MessageEvent) => {
                // Handle incoming data
                if (typeof event.data === 'string') {
                    try {
                        const data = JSON.parse(event.data);
                        if (data.type === 'status') {
                            setStatus(data.message);
                            if (data.code !== undefined) {
                                setStatusCode(data.code);
                                window.parent.postMessage({ type: 'status', code: data.code }, '*');
                            }

                            return;
                        }
                        if (data.type === 'url') {
                            setCurrentUrl(data.url);
                            setUrlInput(data.url);

                            return;
                        }
                        if (data.type === 'agent_message') {
                            window.parent.postMessage({ type: 'agent_message', message: data.message }, '*');
                            return;
                        }
                        if (data.type === 'liveViewLink' && data.url) {
                            setLiveViewLink(data.url);
                        }
                    } catch {
                        // Plain text status message
                        setStatus(event.data);
                    }
                    return;
                }

                // Handle binary image data
                if (event.data instanceof Blob) {
                    const img = new Image();
                    img.onload = () => {
                        const canvas = canvasRef.current;
                        // const overlayCanvas = overlayCanvasRef.current;

                        if (canvas) {
                            const ctx = canvas.getContext('2d');
                            if (ctx) {
                                // Update canvas size to match image
                                canvas.width = img.width;
                                canvas.height = img.height;
                                // overlayCanvas.width = img.width;
                                // overlayCanvas.height = img.height;

                                // Draw the image
                                ctx.drawImage(img, 0, 0);
                            }
                        }

                        // Clean up blob URL
                        URL.revokeObjectURL(img.src);
                    };
                    img.src = URL.createObjectURL(event.data);
                }
            };

            wsRef.current.onerror = () => {
                setStatus('Error connecting to server');
                setConnected(false);
            };

        } catch (error) {
            console.error('WebSocket connection error:', error);
            setStatus('Error creating WebSocket connection');
            setConnected(false);
            setShowReconnectDialog(true);
        }

        // Cleanup on unmount
        return () => {
            console.log("Cleanup WebSocket connection");
            wsRef.current?.close();
            wsRef.current = null;
        };
    }, []);

    useEffect(() => {
        // console.log("initUrl ::: ", initUrl, wsRef.current?.readyState);
        if (initUrl && wsRef.current?.readyState === WebSocket.OPEN) {
            wsRef.current.send(JSON.stringify({
                type: 'start_browser',
                data: {
                    url: initUrl
                }
            }));
        }
    }, [initUrl, wsRef.current?.readyState]);

    useEffect(() => {
        const handleMessage = (event: MessageEvent) => {
            // {
            //     type: 'command',
            //     command: '',
            //     requestId: '',
            // }
            if (event.data.type !== 'command' || !event.data.command) return;
            handleSendInstruction(event.data.command);



        };
        window.addEventListener("message", handleMessage);

        // Cleanup
        return () => {
            window.removeEventListener("message", handleMessage);
        };
    }, []);

    // Handle canvas click
    const handleCanvasClick = (e: React.MouseEvent<HTMLCanvasElement>) => {
        const canvas = canvasRef.current;
        if (canvas && wsRef.current?.readyState === WebSocket.OPEN) {
            const rect = canvas.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;

            // Scale coordinates to match actual canvas size
            const scaleX = canvas.width / rect.width;
            const scaleY = canvas.height / rect.height;
            const actualX = x * scaleX;
            const actualY = y * scaleY;

            // Send click to server
            wsRef.current.send(JSON.stringify({
                type: 'click',
                data: {
                    x: actualX,
                    y: actualY
                }
            }));

            // Show visual feedback
            // drawClickOverlay(x, y);
        }
    };

    const handleMouseWheel = (e: React.WheelEvent<HTMLCanvasElement>) => {
        const canvas = canvasRef.current;
        if (!canvas || wsRef.current?.readyState !== WebSocket.OPEN) return;
        e.preventDefault(); // Prevent default scrolling behavior

        if (e.deltaY < 0) {
            // Scroll up
            wsRef.current.send(JSON.stringify({
                type: 'scroll',
                data: {
                    direction: 'up'
                }
            }));
        } else {
            // Scroll down
            wsRef.current.send(JSON.stringify({
                type: 'scroll',
                data: {
                    direction: 'down'
                }
            }));
        }
    };

    const handleSendInstruction = (instruction: string) => {
        if (wsRef.current?.readyState === WebSocket.OPEN && !!instruction.trim()) {
            wsRef.current.send(JSON.stringify({
                type: 'instruction',
                data: {
                    instruction
                }
            }));
        }
    };

    // Handle reload
    // const handleReload = () => {
    //     wsRef.current?.close();
    //     setShowReconnectDialog(false);
    // };

    // Browser navigation handlers
    const handleBack = () => {
        if (wsRef.current?.readyState === WebSocket.OPEN) {
            wsRef.current.send(JSON.stringify({
                type: 'back'
            }));
        }
    };

    const handleForward = () => {
        if (wsRef.current?.readyState === WebSocket.OPEN) {
            wsRef.current.send(JSON.stringify({
                type: 'forward'
            }));
        }
    };

    const handleBrowserReload = () => {
        if (wsRef.current?.readyState === WebSocket.OPEN) {
            wsRef.current.send(JSON.stringify({
                type: 'reload'
            }));
        }
    };

    const handleUrlFocus = () => {
        setIsEditingUrl(true);
        setUrlInput('');
    };

    const handleUrlBlur = () => {
        setIsEditingUrl(false);
        setUrlInput('');
    };

    const handleUrlKeyDown = (e: React.KeyboardEvent) => {
        if (e.key === 'Escape') {
            setIsEditingUrl(false);
            setUrlInput('');
        }
    };

    return (
        <div className="min-h-screen w-full h-full flex flex-col">
            {/* Reconnect Dialog */}
            {showReconnectDialog && (
                <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                    <div className="bg-white p-6 rounded-lg shadow-xl max-w-md">
                        <h3 className="text-lg font-bold mb-4 text-red-600">Connection Failed</h3>
                        <p className="text-gray-700 mb-4">
                            Failed to connect to the WebSocket server after {maxRetries} attempts.
                            Please check if the server is running and try again.
                        </p>
                        <div className="flex gap-3">
                            <button
                                onClick={() => window.location.reload()}
                                className="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors"
                            >
                                Reload Page
                            </button>
                        </div>
                    </div>
                </div>
            )}

            {/* Browser Header */}
            <div className="bg-gray-100 border-b border-gray-300 px-3 py-2 flex items-center gap-2">
                {/* Navigation Buttons */}
                <div className="flex items-center gap-1">
                    <button
                        onClick={handleBack}
                        disabled={!canGoBack}
                        className={`w-8 h-8 rounded-full flex items-center justify-center transition-colors ${canGoBack
                            ? 'hover:bg-gray-200 text-gray-700'
                            : 'text-gray-400 cursor-not-allowed'
                            }`}
                        title="Back"
                    >
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                            <path d="M15 18l-6-6 6-6" />
                        </svg>
                    </button>
                    <button
                        onClick={handleForward}
                        disabled={!canGoForward}
                        className={`w-8 h-8 rounded-full flex items-center justify-center transition-colors ${canGoForward
                            ? 'hover:bg-gray-200 text-gray-700'
                            : 'text-gray-400 cursor-not-allowed'
                            }`}
                        title="Forward"
                    >
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                            <path d="M9 18l6-6-6-6" />
                        </svg>
                    </button>
                    <button
                        onClick={handleBrowserReload}
                        disabled={!connected}
                        className={`w-8 h-8 rounded-full flex items-center justify-center transition-colors ${connected
                            ? 'hover:bg-gray-200 text-gray-700'
                            : 'text-gray-400 cursor-not-allowed'
                            }`}
                        title="Reload"
                    >
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                            <path d="M1 4v6h6" />
                            <path d="M3.51 15a9 9 0 1 0 2.13-9.36L1 10" />
                        </svg>
                    </button>
                </div>

                {/* URL Bar */}
                <div className="flex-1 mx-3">
                    <form className="relative">
                        <div className="relative flex items-center">
                            {/* Lock/Security Icon */}
                            <div className="absolute left-3 flex items-center">
                                <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" className="text-gray-500">
                                    <rect x="3" y="11" width="18" height="11" rx="2" ry="2" />
                                    <circle cx="12" cy="7" r="4" />
                                    <path d="M12 1v6" />
                                </svg>
                            </div>

                            <input
                                type="text"
                                value={isEditingUrl ? urlInput : currentUrl}
                                onChange={(e) => setUrlInput(e.target.value)}
                                onFocus={handleUrlFocus}
                                onBlur={handleUrlBlur}
                                onKeyDown={handleUrlKeyDown}
                                placeholder="Enter URL..."
                                disabled={!connected}
                                className={`w-full pl-8 pr-10 py-1.5 text-sm border rounded-full transition-colors ${connected
                                    ? 'bg-white border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent'
                                    : 'bg-gray-100 border-gray-200 text-gray-400 cursor-not-allowed'
                                    } ${isEditingUrl ? 'bg-white' : 'bg-gray-50'}`}
                            />

                            {/* Submit Button (when editing) */}
                            {isEditingUrl && urlInput.trim() && (
                                <button
                                    type="submit"
                                    className="absolute right-2 w-6 h-6 rounded-full bg-blue-500 hover:bg-blue-600 flex items-center justify-center transition-colors"
                                    title="Navigate"
                                >
                                    <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="white" strokeWidth="2">
                                        <path d="M5 12h14" />
                                        <path d="M12 5l7 7-7 7" />
                                    </svg>
                                </button>
                            )}
                        </div>
                    </form>
                </div>

                {/* Status Indicator */}
                <div className="flex items-center gap-2">
                    <div className={`w-2 h-2 rounded-full ${connected ? 'bg-green-500' : 'bg-red-500'}`} title={status}></div>
                    <span className="text-xs text-gray-500 hidden sm:inline max-w-32 truncate" title={status}>
                        {status}
                    </span>
                </div>
            </div>

            {/* Canvas Container */}
            <div className="flex-1 flex items-center justify-center p-4 min-h-0">
                {
                    statusCode === 'initializing' && (
                        <div className="absolute inset-0 flex items-center justify-center bg-gray-100">
                            <span className="text-gray-500">Initializing...</span>
                        </div>
                    )
                }
                <div className={`relative border-2 border-gray-400 rounded-lg overflow-hidden shadow-lg bg-white max-w-full max-h-full ${statusCode === 'running' ? 'block' : 'hidden'}`}>
                    {
                        !liveViewLink && (
                            <canvas
                                ref={canvasRef}
                                className={`max-w-full max-h-full object-contain cursor-crosshair block`}
                                onClick={handleCanvasClick}
                                onWheel={handleMouseWheel}
                            />
                        )
                    }
                    {
                        liveViewLink && (
                            <iframe
                                src={liveViewLink}
                                sandbox="allow-same-origin allow-scripts"
                                allow="clipboard-read; clipboard-write"
                                style={{ pointerEvents: 'none' }}
                            />
                        )
                    }
                </div>
            </div>
        </div>
    );
};

export default memo(BrowserStream);
