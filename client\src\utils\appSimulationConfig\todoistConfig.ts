// TODO: fetch data from db

import { AppSimulationScreen } from '~/common';

export const todoistScreens: AppSimulationScreen[] = [
  {
    id: '68784de54eebbd5ab6621222',
    image:
      'https://bitcountry-hub-assets.s3.ap-southeast-1.amazonaws.com/images/6801b0712e63bcecf8293e5c/1752741870366__1_list.png',
    title: '1',
    elements: [
      {
        x1: 19.150641025641026,
        y1: 41.66705271151126,
        x2: 26.241987179487182,
        y2: 44.16374694855254,
        title: 'Add Task',
        actions: [
          {
            type: 'nextScreen',
            screenId: '68784de54eebbd5ab6621223',
            id: '68784df34eebbd5ab662122b',
          },
        ],
        id: '68784df34eebbd5ab662122a',
      },
    ],
    bgColor: '#64767e',
  },
  {
    id: '68784de54eebbd5ab6621223',
    image:
      'https://bitcountry-hub-assets.s3.ap-southeast-1.amazonaws.com/images/6801b0712e63bcecf8293e5c/1752741870367__2_create_task.png',
    title: '2',
    elements: [
      {
        x1: 20.192307692307693,
        y1: 19.141322483983203,
        x2: 62.0,
        y2: 22.0263913801198,
        title: 'Add Summary',
        actions: [
          {
            type: 'inputText',
            dataContextId: 'summary',
            inputTextType: 'input',
            dataContextLabel: 'Summary',
            id: '68784e794eebbd5ab662123c',
          },
        ],
        id: '68784e794eebbd5ab662123b',
      },
      {
        x1: 20.192307692307693,
        y1: 22.470248133371587,
        x2: 62.05929487179487,
        y2: 46.327548620654994,
        title: 'Add Description',
        actions: [
          {
            type: 'inputText',
            dataContextId: 'description',
            inputTextType: 'textarea',
            dataContextLabel: 'Description',
            id: '68784eba4eebbd5ab6621254',
          },
        ],
        id: '68784eba4eebbd5ab6621253',
      },
      {
        x1: 50.881410256410255,
        y1: 47.43719050378446,
        x2: 56.53044871794872,
        y2: 51.209972906424625,
        title: 'Cancel',
        actions: [
          {
            type: 'nextScreen',
            screenId: '68784de54eebbd5ab6621222',
          },
        ],
        id: '68785a5d4eebbd5ab6621280',
      },
      {
        x1: 56.891025641025635,
        y1: 47.43719050378446,
        x2: 62.33974358974359,
        y2: 51.09900871811168,
        title: 'Save',
        actions: [
          {
            type: 'nextScreen',
            screenId: '68784de54eebbd5ab6621222',
          },
        ],
        id: '68785a5d4eebbd5ab6621280',
      },
    ],
    placeholders: [
      {
        x1: 20.272435897435898,
        y1: 19.252286672296147,
        x2: 62.019230769230774,
        y2: 22.13735556843275,
        title: 'placeholder_summary',
        dataId: 'summary',
        type: 'text',
        style: {
          fontWeight: 'bold',
          whiteSpace: 'normal',
          overflow: 'hidden',
          alignItems: 'center',
          textOverflow: 'clip',
        },
        id: '68784e2f4eebbd5ab6621232',
      },
      {
        x1: 20.272435897435898,
        y1: 22.525730227528058,
        x2: 62.019230769230774,
        y2: 46.327548620654994,
        title: 'placeholder_description',
        dataId: 'description',
        type: 'text',
        style: {
          fontWeight: 'normal',
          whiteSpace: 'pre-line',
          overflow: 'auto',
          alignItems: 'normal',
          textOverflow: 'clip',
        },
        id: '68784e9a4eebbd5ab6621244',
      },
    ],
    bgColor: '#64767e',
  },
];
