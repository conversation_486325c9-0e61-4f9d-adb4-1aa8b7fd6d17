import { memo } from 'react';

interface BrowserStreamBrowserBaseProps {
  liveViewLink: string;
}

const BrowserStreamBrowserBase = ({ liveViewLink }: BrowserStreamBrowserBaseProps) => {

  return (
    <iframe
      src={liveViewLink}
      sandbox="allow-same-origin allow-scripts"
      allow="clipboard-read; clipboard-write"
      className='w-full h-full'
    />
  );
};

export default memo(BrowserStreamBrowserBase);
