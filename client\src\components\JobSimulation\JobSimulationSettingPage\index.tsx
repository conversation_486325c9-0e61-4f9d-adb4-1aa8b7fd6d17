import { useState } from 'react';
import AppCreation from './AppCreation';
import Screen from './Screens';
import { useRecoilValue } from 'recoil';
import store from '~/store';
import type t from 'librechat-data-provider';

export default function JobSimulationSettingPage() {
  const [appSelected, setAppSelected] = useState<t.TJobSimulationApp | null>(null);
  const user = useRecoilValue(store.user);

  return (
    <div className="flex flex-col gap-4 p-8">
      <div className="flex h-full w-full text-white">
        <AppCreation setAppSelected={setAppSelected} appSelected={appSelected} user={user} />
      </div>

      {appSelected && (
        <div className="pt-4 text-white">
          <Screen user={user} appSelected={appSelected} />
        </div>
      )}
    </div>
  );
}
