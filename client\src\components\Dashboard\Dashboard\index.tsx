import JobConversionFunnel from './JobConversionFunnel';
import OverviewStats from './OverviewStats';
import TopPerformingJobSimsChart from './TopPerformingJobSimsChart';
import TopTalentTable from './TopTalentTable';

const Dashboard = () => {
  return (
    <div className="p-4">
      <div className="mb-8 rounded-xl bg-white p-6 text-[#505050] drop-shadow-lg">
        <div className="mb-5 flex flex-col items-center justify-between gap-4 md:flex-row">
          <div>
            <p className="text-xl font-medium">Overview</p>
            <p className="text-sm">
              Track the performance, engagement, and talent potential across all Job Simulations
            </p>
          </div>
          {/* <div className="flex space-x-2 bg-[#F8F8FF] py-1 px-2 rounded-full">
            {["day", "week", "month", 'all'].map((range) => (
              <Button
                key={range}
                size="sm"
                onClick={() => setActiveRange(range as "day" | "week" | "month" | 'all')}
                className={cn(
                  "text-xs rounded-full capitalize",
                  activeRange === range
                    ? "bg-gray-900 text-white"
                    : "bg-transparent hover:bg-transparent text-gray-600"
                )}
              >
                {range}
              </Button>
            ))}
          </div> */}
        </div>

        {/* Stats Cards */}
        <OverviewStats />
      </div>

      {/* Charts Row */}
      <div className="mb-6 grid grid-cols-1 gap-6">
        <TopPerformingJobSimsChart />
      </div>
      <div className="mb-6 grid grid-cols-1">
        <TopTalentTable />
        {/* <JobConversionFunnel /> */}
      </div>

      {/* Bottom Row */}
      {/* <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
        <TopTalentTable />
        <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
          <UserDistributionPieChart />
          <TopSkillsTable />
        </div>
      </div> */}
    </div>
  );
};

export default Dashboard;
