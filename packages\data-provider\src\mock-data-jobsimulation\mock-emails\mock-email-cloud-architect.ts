import { TJobSimulationEmail } from 'src/types';

const getEmails = (data: {
  jobSimulationId: string;
  logo: string;
  billionIntakeId?: string;
}): TJobSimulationEmail[] => [
  {
    id: '1',
    name: 'HR NimbusWorks',
    avatar: 'https://d14ciuzrn5ydd5.cloudfront.net/bitmeet/tutor-ai/image-3.jpg',
    role: 'HR Manager',
    email: '<EMAIL>',
    title: 'Welcome to NimbusWorks Cloud Architecture Simulation!',
    desc: "Welcome aboard! We're excited to have you join NimbusWorks as a Cloud Architect Intern.",
    nextEmailId: '2',
    data: {
      logo: data.logo,
      greeting: 'Hi {user_name}',
      content: `I'm **HR Manager at NimbusWorks**, and I'm thrilled to welcome you to our cloud innovation team!

This simulation will give you hands-on experience with real-world cloud architecture scenarios, helping you develop critical design, analysis, and optimization skills.

Let's get started with a short virtual kickoff meeting to introduce the team and set expectations. I'll be scheduling it shortly. Looking forward to seeing you!`,
      signature: {
        title: 'Regards',
        company: 'NimbusWorks',
      },
    },
    triggerActions: [
      {
        type: 'nextEmail',
        data: { nextEmailId: '2', triggerTimeout: 3, when: 'open' },
      },
      {
        type: 'enableApps',
        data: {
          appIds: ['mail', 'news', 'meeting'],
          when: 'receive',
        },
      },
    ],
  },
  {
    id: '2',
    name: 'Liam Parker',
    role: 'Cloud Engineering Manager',
    avatar: 'https://beta.bitmeet.io/files/1748226552291-98529265-digital_marketing_onboard.jpg',
    email: '<EMAIL>',
    title: 'Cloud Architect Kickoff Meeting',
    desc: "Let's align on goals and tasks for your cloud architecture simulation.",
    nextEmailId: '3',
    data: {
      logo: data.logo,
      greeting: 'Hi {user_name}',
      content: `I'm **Liam Parker**, Cloud Engineering Manager at NimbusWorks.

In this simulation, you'll work through two core architecture tasks:
* Evaluating and modernizing a legacy system for cloud
* Scaling and improving performance under growing demand

Let's start with a quick onboarding session to introduce tools, context, and what we're expecting from your work.

See you soon!`,
      actions: [
        {
          type: 'joinMeeting',
          label: 'Join Kickoff Meeting',
          title: 'Onboarding Meeting',
          data: {
            datetime: '{email_time}',
            duration: '~1 minutes',
            meetingLink: 'https://dev-bitmeet.mnet.io/introduction/a2d-400-3f9?t=mi',
            completionMeetingActions: [
              {
                type: 'triggerAssistant',
                data: {
                  triggerMessage: 'I have completed the onboarding meeting with Liam.',
                },
              },
              {
                type: 'enableApps',
                data: {
                  appIds: ['mail', 'news', 'meeting', 'task-board'],
                },
              },
              {
                type: 'sendEmailTask',
              },
            ],
          },
        },
      ],
      signature: {
        title: 'Warm regards',
        company: 'NimbusWorks',
      },
    },
    triggerActions: [
      {
        type: 'triggerAssistant',
        data: {
          triggerTimeout: 1,
          triggerMessage: "I've received the kickoff meeting invitation.",
          when: 'receive',
        },
      },
    ],
  },
  {
    id: '3',
    name: 'Liam Parker',
    role: 'Cloud Engineering Manager',
    avatar: 'https://beta.bitmeet.io/files/1748226552291-98529265-digital_marketing_onboard.jpg',
    email: '<EMAIL>',
    title: 'Simulation Complete - Thank You!',
    desc: 'Great work completing your cloud architecture simulation.',
    data: {
      logo: data.logo,
      greeting: 'Hi {user_name}',
      content: `You've successfully completed the **Cloud Architecture Simulation** at NimbusWorks.

You've demonstrated solid design thinking, cloud planning, and problem-solving skills.

Thank you for participating!

Here's your reference letter:`,
      actions: [
        {
          type: 'viewFileDetail',
          label: 'View Reference Letter',
          title: 'Reference Letter',
          data: {
            fileUrl: `https://uat.internship.guru/en/public/reference-letter?programId=${data.billionIntakeId}&autoClaim=true`,
          },
        },
      ],
      signature: {
        title: 'Regards',
        company: 'NimbusWorks',
      },
    },
  },
];

const buildEmailTask = (prams: { jobSimulation: any; task: any }): TJobSimulationEmail => {
  const { jobSimulation, task } = prams;
  return {
    id: `email-task:${task.taskId}`,
    type: 'task',
    name: 'Liam Parker',
    avatar: 'https://beta.bitmeet.io/files/1748226552291-98529265-digital_marketing_onboard.jpg',
    role: 'Cloud Engineering Manager',
    email: '<EMAIL>',
    title: `Task: ${task.taskName}`,
    desc: `New task assigned: ${task.taskName}`,
    data: {
      taskId: task.taskId,
      programId: jobSimulation.billionIntakeId,
      logo: jobSimulation.logo,
      greeting: 'Hi {user_name}',
      content: `You're progressing well in the **Cloud Architect Simulation** at NimbusWorks!

Your next task is titled: **${task.taskName}**

${task.taskDescription}

Please review the task carefully and complete it at your earliest convenience.
**You can reply directly to this email with your submission.**

We're excited to see your solution!`,
      actions: [],
      signature: {
        title: 'Best regards',
        company: jobSimulation.companyName,
      },
    },
    allowReply: true,
    triggerActions: [
      {
        type: 'enableApps',
        data: {
          appIds: ['mail', 'news', 'meeting', 'task-board'],
          when: 'receive',
        },
      },
    ],
  };
};

export default { getEmails, buildEmailTask };
