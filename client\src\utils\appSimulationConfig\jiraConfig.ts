// TODO: fetch data from db

import { AppSimulationScreen } from '~/common';

export const jiraScreens: AppSimulationScreen[] = [
  {
    id: '6878a0abb1a37b2a48642cda',
    image:
      'https://bitcountry-hub-assets.s3.ap-southeast-1.amazonaws.com/images/6801b0712e63bcecf8293e5c/1752735914443__01_board_3830x1840.png',
    title: '01_board_3830x1840',
    elements: [
      {
        x1: 14.703525641025642,
        y1: 37.61101820146747,
        x2: 19.190705128205128,
        y2: 41.28038583087893,
        title: 'create bug 01',
        actions: [
          {
            type: 'nextScreen',
            screenId: '6878a0abb1a37b2a48642cdb',
            id: '6878a0e2b1a37b2a48642ce5',
          },
        ],
        id: '6878a0e2b1a37b2a48642ce4',
      },
      {
        x1: 66.54647435897436,
        y1: 0.5003683131015628,
        x2: 71.15384615384616,
        y2: 4.253130661363284,
        title: 'create bug 02',
        actions: [
          {
            type: 'nextScreen',
            screenId: '6878a0abb1a37b2a48642cdb',
            id: '6878a0f5b1a37b2a48642cee',
          },
        ],
        id: '6878a0f5b1a37b2a48642ced',
      },
    ],
    bgColor: '#64767e',
  },
  {
    id: '6878a0abb1a37b2a48642cdb',
    image:
      'https://bitcountry-hub-assets.s3.ap-southeast-1.amazonaws.com/images/6801b0712e63bcecf8293e5c/1752735914443__02_createbug_3830x1870.png',
    title: '02_createbug_3830x1870',
    elements: [
      {
        x1: 32.25,
        y1: 47.83917008745691,
        x2: 68,
        y2: 51.8,
        title: 'summary',
        actions: [
          {
            type: 'inputText',
            dataContextId: 'summary',
            inputTextType: 'input',
            dataContextLabel: 'Summary',
            id: '6878a181b1a37b2a48642d0a',
          },
        ],
        id: '6878a181b1a37b2a48642d09',
      },
      {
        x1: 32.29166666666667,
        y1: 59.655363042163245,
        x2: 68,
        y2: 81.5,
        title: 'description',
        actions: [
          {
            type: 'inputText',
            dataContextId: 'description',
            inputTextType: 'textarea',
            dataContextLabel: 'Description',
            id: '6878a1a6b1a37b2a48642d17',
          },
        ],
        id: '6878a1a6b1a37b2a48642d16',
      },
      {
        x1: 32.22,
        y1: 85.4,
        x2: 46.99519230769231,
        y2: 89.4,
        title: 'Priority',
        actions: [
          {
            type: 'dropdown',
            dropdownOptions: [
              {
                label: 'Highest',
                value: 'Highest',
                dataContextId: 'priority',
              },
              {
                label: 'High',
                value: 'High',
                dataContextId: 'priority',
              },
              {
                label: 'Medium',
                value: 'Medium',
                dataContextId: 'priority',
              },
              {
                label: 'Low',
                value: 'Low',
                dataContextId: 'priority',
              },
              {
                label: 'Lowest',
                value: 'Lowest',
                dataContextId: 'priority',
              },
            ],
            id: '6878a241b1a37b2a48642d26',
          },
        ],
        id: '6878a241b1a37b2a48642d25',
      },
      {
        x1: 60.61698717948718,
        y1: 93.54486089142516,
        x2: 64.1025641025641,
        y2: 96.99125050321452,
        title: 'cancel',
        actions: [
          {
            type: 'nextScreen',
            screenId: '6878a0abb1a37b2a48642cda',
            id: '6878a259b1a37b2a48642d3f',
          },
        ],
        id: '6878a259b1a37b2a48642d3e',
      },
      {
        x1: 64.50320512820514,
        y1: 93.4,
        x2: 68.18910256410257,
        y2: 97.1553642942521,
        title: 'create',
        actions: [
          {
            type: 'nextScreen',
            screenId: '6878a0abb1a37b2a48642cdc',
            id: '6878a26eb1a37b2a48642d56',
          },
        ],
        id: '6878a26eb1a37b2a48642d55',
      },
    ],
    placeholders: [
      {
        x1: 32.331730769230774,
        y1: 48.00328387849449,
        x2: 67.90865384615384,
        y2: 51.53173038580263,
        title: 'placeholder_summary',
        dataId: 'summary',
        type: 'text',
        style: {
          fontWeight: 'normal',
          whiteSpace: 'normal',
          overflow: 'hidden',
          alignItems: 'center',
          textOverflow: 'clip',
        },
        id: '6878a136b1a37b2a48642cf5',
      },
      {
        x1: 32.331730769230774,
        y1: 59.710068056538724,
        x2: 67.90865384615384,
        y2: 81.04486089142516,
        title: 'placeholder_description',
        dataId: 'description',
        type: 'text',
        style: {
          fontWeight: 'normal',
          // whiteSpace: 'normal',
          overflow: 'auto',
          alignItems: 'normal',
          // textOverflow: 'clip',
        },
        id: '6878a15db1a37b2a48642cfe',
      },
      {
        x1: 32.5,
        y1: 85.7,
        x2: 45,
        y2: 89,
        title: 'Priority',
        dataId: 'priority',
        initialValue: 'High',
        type: 'text',
        style: {
          fontWeight: 'normal',
          whiteSpace: 'normal',
          overflow: 'visible',
          alignItems: 'center',
          textOverflow: 'clip',
        },
        id: '6878a15db1a37b2a48642cfp',
      },
    ],
    bgColor: '#64767e',
  },
  {
    id: '6878a0abb1a37b2a48642cdc',
    image:
      'https://bitcountry-hub-assets.s3.ap-southeast-1.amazonaws.com/images/6801b0712e63bcecf8293e5c/1752735914443__03_done_create_bug.png',
    title: '03_done_create_bug',
    elements: [
      {
        x1: 14.743589743589745,
        y1: 56.79180353702738,
        x2: 19.030448717948715,
        y2: 59.79401341563675,
        title: 'create 1',
        actions: [
          {
            type: 'nextScreen',
            screenId: '6878a0abb1a37b2a48642cdb',
            id: '6878a2d7b1a37b2a48642d76',
          },
        ],
        id: '6878a2d7b1a37b2a48642d75',
      },
      {
        x1: 66.58653846153845,
        y1: 0.5837630319518233,
        x2: 71.11378205128204,
        y2: 4.169735942513023,
        title: 'create 2',
        actions: [
          {
            type: 'nextScreen',
            screenId: '6878a0abb1a37b2a48642cdb',
            id: '6878a2ebb1a37b2a48642d81',
          },
        ],
        id: '6878a2ebb1a37b2a48642d80',
      },
    ],
    placeholders: [
      {
        x1: 15.104166666666666,
        y1: 38.861938984221375,
        x2: 25.841346153846157,
        y2: 51.871515124862015,
        title: 'placeholder_description',
        dataId: 'summary',
        type: 'text',
        style: {
          fontWeight: 'normal',
          whiteSpace: 'normal',
          overflow: 'auto',
          alignItems: 'normal',
          textOverflow: 'clip',
        },
        id: '6878a2c4b1a37b2a48642d6c',
      },
    ],
    bgColor: '#64767e',
  },
];
