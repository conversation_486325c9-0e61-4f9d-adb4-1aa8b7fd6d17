import { getAppData } from '~/components/JobSimulation/AppSimulation/AppDataStorage';
import AppSimulation from './AppSimulation';
// import { useListJobSimulationAppScreenByUserAndApp } from '~/data-provider/JobSimulation';

interface AppSimulationWrapperProps {
  appSimulationId: string;
}

const AppSimulationWrapper = ({ appSimulationId }: AppSimulationWrapperProps) => {
  const appData = getAppData(appSimulationId);
  // const { data: screenData } = useListJobSimulationAppScreenByUserAndApp({
  //   appId:  appSimulationId, // "6864b192c30283a0614fe039"
  //   page: 1,
  //   limit: 100,
  // });
  if (!appData?.appSimulationScreens) {
    return <></>;
  }
  return (
    <AppSimulation
      appSimulationScreens={appData.appSimulationScreens}
      appSimulationConfig={appData.appSimulationConfig}
    />
  );
};

export default AppSimulationWrapper;
