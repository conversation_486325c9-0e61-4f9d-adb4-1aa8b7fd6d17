const { uploadFileToS3 } = require('~/server/services/Files/S3/crud');
const { jobSimulationAppScreen, getUserById, jobSimulationApp } = require('~/models');

const JobSimulationAppScreenService = {
  async createMany(req) {
    const { body, files } = req;

    try {
      if (!body.appId || !body.userId) {
        throw new Error('App ID and User ID are required to create job simulation app screens');
      }
      const user = await getUserById(body.userId);
      if (!user) {
        throw new Error('User not found');
      }
      const app = await jobSimulationApp.getById(body.appId);
      if (!app) {
        throw new Error('Job simulation app not found');
      }
      if (!files || !files.length) {
        throw new Error('At least one file is required to create a job simulation app screen');
      }
      const uploadResults = await Promise.all(
        files.map((file) => uploadFileToS3({ req, file, file_id: Date.now() })),
      );

      const fileInfo = uploadResults
        .map((result) => {
          if (!result.filepath) return null;

          const fullName =
            result.originalname ||
            files[uploadResults.indexOf(result)]?.originalname ||
            'Unnamed file';

          const nameWithoutExtension = fullName.replace(/\.[^/.]+$/, '');

          return {
            image: result.filepath,
            name: nameWithoutExtension,
          };
        })
        .filter(Boolean);
      if (fileInfo.length === 0) {
        throw new Error('Failed to upload files for job simulation app screen');
      }

      const screens = fileInfo.map((info) => ({
        ...body,
        ...info,
      }));

      const createdScreens = await jobSimulationAppScreen.createMany(screens);

      return createdScreens;
    } catch (error) {
      throw new Error(`Failed to create job simulation app screen: ${error.message}`);
    }
  },

  async update(req) {
    const { body, file, params } = req;

    try {
      const updateData = { ...body };
      if (file) {
        const uploadResult = await uploadFileToS3({ req, file, file_id: Date.now() });
        if (!uploadResult?.filepath) return null;
        updateData.image = uploadResult.filepath;
      }
      delete updateData.order;

      const updatedScreen = await jobSimulationAppScreen.update(params.id, updateData);
      return updatedScreen;
    } catch (error) {
      throw new Error(`Failed to update job simulation app screen: ${error.message}`);
    }
  },

  async updateOrder(params) {
    try {
      const updatedScreens = await jobSimulationAppScreen.updateOrder(params);
      return updatedScreens;
    } catch (error) {
      throw new Error(`Failed to update job simulation app screen order: ${error.message}`);
    }
  },

  async deleteById(id, params) {
    try {
      const deletedScreen = await jobSimulationAppScreen.deleteById(id, params);
      return deletedScreen;
    } catch (error) {
      throw new Error(`Failed to delete job simulation app screen: ${error.message}`);
    }
  },

  async getByUserAndApp(params) {
    try {
      const screens = await jobSimulationAppScreen.getByUserAndApp(params);
      return screens;
    } catch (error) {
      throw new Error(`Failed to retrieve job simulation app screens: ${error.message}`);
    }
  },

  async getById(id) {
    try {
      const screen = await jobSimulationAppScreen.getById(id);
      return screen;
    } catch (error) {
      throw new Error(`Failed to retrieve job simulation app screen: ${error.message}`);
    }
  },
};

module.exports = JobSimulationAppScreenService;
