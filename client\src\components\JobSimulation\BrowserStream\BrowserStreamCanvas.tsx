import React, { memo, useRef, useState } from 'react';
import { cn } from '~/utils';

interface BrowserStreamCanvasProps {
  status: string;
  currentUrl: string;
  canvasRef: React.RefObject<HTMLCanvasElement>;
  sendWsData: (obj: any) => void;
}

const BrowserStreamCanvas = ({ status, currentUrl, canvasRef, sendWsData }: BrowserStreamCanvasProps) => {
  const connected = true;
  const [urlInput, setUrlInput] = useState<string>('');
  const [isEditingUrl, setIsEditingUrl] = useState(false);
  const [canGoBack, _setCanGoBack] = useState(false);
  const [canGoForward, _setCanGoForward] = useState(false);

  // Handle canvas click
  const handleCanvasClick = (e: React.MouseEvent<HTMLCanvasElement>) => {
    const canvas = canvasRef.current;
    if (!canvas) return;
    const rect = canvas.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;

    // Scale coordinates to match actual canvas size
    const scaleX = canvas.width / rect.width;
    const scaleY = canvas.height / rect.height;
    const actualX = x * scaleX;
    const actualY = y * scaleY;
    sendWsData({
      type: 'click',
      data: {
        x: actualX,
        y: actualY
      }
    });
  };

  const handleMouseWheel = (e: React.WheelEvent<HTMLCanvasElement>) => {
    if (!canvasRef.current) return;
    e.preventDefault(); // Prevent default scrolling behavior

    const direction = e.deltaY < 0 ? 'up' : 'down';
    sendWsData({
      type: 'scroll',
      data: {
        direction
      }
    });
  };

  // Handle reload
  // const handleReload = () => {
  //     wsRef.current?.close();
  //     setShowReconnectDialog(false);
  // };

  // Browser navigation handlers
  const handleBack = () => {
    sendWsData({
      type: 'back'
    });
  };

  const handleForward = () => {
    sendWsData({
      type: 'forward'
    });
  };

  const handleBrowserReload = () => {
    sendWsData({
      type: 'reload'
    });
  };

  const handleUrlFocus = () => {
    setIsEditingUrl(true);
    setUrlInput('');
  };

  const handleUrlBlur = () => {
    setIsEditingUrl(false);
    setUrlInput('');
  };

  const handleUrlKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      setIsEditingUrl(false);
      setUrlInput('');
    }
  };

  return (
    <>
      <div className="bg-gray-100 border-b border-gray-300 px-3 py-2 flex items-center gap-2">
        {/* Navigation Buttons */}
        <div className="flex items-center gap-1">
          <button
            onClick={handleBack}
            disabled={!canGoBack}
            className={cn(
              'w-8 h-8 rounded-full flex items-center justify-center transition-colors',
              canGoBack ? 'hover:bg-gray-200 text-gray-700' : 'text-gray-400 cursor-not-allowed',
            )}
            title="Back"
          >
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <path d="M15 18l-6-6 6-6" />
            </svg>
          </button>
          <button
            onClick={handleForward}
            disabled={!canGoForward}
            className={cn(
              'w-8 h-8 rounded-full flex items-center justify-center transition-colors',
              canGoForward ? 'hover:bg-gray-200 text-gray-700' : 'text-gray-400 cursor-not-allowed',
            )}
            title="Forward"
          >
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <path d="M9 18l6-6-6-6" />
            </svg>
          </button>
          <button
            onClick={handleBrowserReload}
            disabled={!connected}
            className={cn(
              'w-8 h-8 rounded-full flex items-center justify-center transition-colors',
              connected ? 'hover:bg-gray-200 text-gray-700' : 'text-gray-400 cursor-not-allowed',
            )}
            title="Reload"
          >
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <path d="M1 4v6h6" />
              <path d="M3.51 15a9 9 0 1 0 2.13-9.36L1 10" />
            </svg>
          </button>
        </div>

        {/* URL Bar */}
        <div className="flex-1 mx-3">
          <form className="relative">
            <div className="relative flex items-center">
              {/* Lock/Security Icon */}
              <div className="absolute left-3 flex items-center">
                <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" className="text-gray-500">
                  <rect x="3" y="11" width="18" height="11" rx="2" ry="2" />
                  <circle cx="12" cy="7" r="4" />
                  <path d="M12 1v6" />
                </svg>
              </div>

              <input
                type="text"
                value={isEditingUrl ? urlInput : currentUrl}
                onChange={(e) => setUrlInput(e.target.value)}
                onFocus={handleUrlFocus}
                onBlur={handleUrlBlur}
                onKeyDown={handleUrlKeyDown}
                placeholder="Enter URL..."
                disabled={!connected}
                className={cn(
                  'w-full pl-8 pr-10 py-1.5 text-sm border rounded-full transition-colors',
                  connected ? 'bg-white border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent' : 'bg-gray-100 border-gray-200 text-gray-400 cursor-not-allowed',
                  isEditingUrl ? 'bg-white' : 'bg-gray-50',
                )}
              />

              {/* Submit Button (when editing) */}
              {isEditingUrl && urlInput.trim() && (
                <button
                  type="submit"
                  className="absolute right-2 w-6 h-6 rounded-full bg-blue-500 hover:bg-blue-600 flex items-center justify-center transition-colors"
                  title="Navigate"
                >
                  <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="white" strokeWidth="2">
                    <path d="M5 12h14" />
                    <path d="M12 5l7 7-7 7" />
                  </svg>
                </button>
              )}
            </div>
          </form>
        </div>

        {/* Status Indicator */}
        <div className="flex items-center gap-2">
          <div className={`w-2 h-2 rounded-full ${connected ? 'bg-green-500' : 'bg-red-500'}`} title={status}></div>
          <span className="text-xs text-gray-500 hidden sm:inline max-w-32 truncate" title={status}>
            {status}
          </span>
        </div>
      </div>

      <div className="flex-1 flex items-center justify-center p-4 min-h-0">
        <div className="relative border-2 border-gray-400 rounded-lg overflow-hidden shadow-lg bg-white max-w-full max-h-full block">
          <canvas
            ref={canvasRef}
            className={`max-w-full max-h-full object-contain cursor-crosshair block`}
            onClick={handleCanvasClick}
            onWheel={handleMouseWheel}
          />
        </div>
      </div>
    </>
  );
};

export default memo(BrowserStreamCanvas);
