import { ChevronLeft, ChevronRight, Eye, Search } from "lucide-react";
import { useEffect, useState } from "react";
import { dataService } from 'librechat-data-provider';
import { Button, Input } from "~/components/ui";
import { cn } from "~/utils";
import TestimonialCard from "./TestimonialCard";
import { useParams } from "react-router-dom";

const JobSimulationTalentsTable = ({ selectedJob }: { selectedJob: "esg-analyst" | "digital-marketing" }) => {
	const params = useParams();
	const tab = params.tab as 'esg-analyst' | 'digital-marketing';
	const [feedback, setFeedback] = useState<any | null>(null);
	const [isLoading, setIsLoading] = useState<boolean>(false);

	const getFeedbacks = async (params: any): Promise<any> => {
		const result = await dataService.getJobSimulationFeedbackByJobId(params.jobSimulationId);
		return result
	};

	const [activeTab, setActiveTab] = useState<"talent" | "feedback">("talent");

	const talentsData = {
		"esg-analyst": [
			{ rank: 1, name: "<PERSON>", task: "2/2", score: 97, time: "2 hours", medal: "🥇" },
			{ rank: 2, name: "Cameron Williamson", task: "2/2", score: 95, time: "2.3 hours", medal: "🥈" },
			{ rank: 3, name: "Jenny <PERSON>", task: "2/2", score: 93, time: "3 hours", medal: "🥉" },
			{ rank: 4, name: "Wade Warren", task: "2/2", score: 91, time: "2.1 hours", medal: "4" },
			{ rank: 5, name: "Floyd Miles", task: "2/2", score: 90, time: "3 hours", medal: "5" },
			{ rank: 6, name: "Courtney Henry", task: "2/2", score: 89, time: "3 hours", medal: "6" },
			{ rank: 7, name: "Savannah Nguyen", task: "2/2", score: 88, time: "3 hours", medal: "7" },
			{ rank: 8, name: "Marvin McKinney", task: "2/2", score: 82, time: "3 hours", medal: "8" },
			{ rank: 9, name: "Darlene Robertson", task: "2/2", score: 72, time: "3 hours", medal: "9" },
			{ rank: 10, name: "Leslie Alexander", task: "1/2", score: 50, time: "3 hours", medal: "10" },
		],
		"digital-marketing": [
			{ rank: 1, name: "Alice Johnson", task: "3/3", score: 98, time: "1.5 hours", medal: "🥇" },
			{ rank: 2, name: "Bob Smith", task: "3/3", score: 96, time: "1.8 hours", medal: "🥈" },
			{ rank: 3, name: "Charlie Brown", task: "3/3", score: 94, time: "2 hours", medal: "🥉" },
			{ rank: 4, name: "Diana Prince", task: "3/3", score: 92, time: "2.2 hours", medal: "4" },
			{ rank: 5, name: "Ethan Hunt", task: "3/3", score: 90, time: "2.5 hours", medal: "5" },
			{ rank: 6, name: "Fiona Gallagher", task: "3/3", score: 88, time: "2.7 hours", medal: "6" },
			{ rank: 7, name: "George Lucas", task: "3/3", score: 85, time: "3 hours", medal: "7" },
			{ rank: 8, name: "Hannah Baker", task: "3/3", score: 80, time: "3.2 hours", medal: "8" },
			{ rank: 9, name: "Ian Somerhalder", task: "2/3", score: 75, time: "3.5 hours", medal: "9" },
			{ rank: 10, name: "Jane Doe", task: "2/3", score: 60, time: "4 hours", medal: "10" },
		],
	};
	const talents = talentsData[selectedJob];

	useEffect(() => {
		if (!tab) return;
		setIsLoading(true);
		getFeedbacks({ jobSimulationId: tab })
			.then((data) => {
				setFeedback(data);
			})
			.finally(() => {
				setIsLoading(false);
			});
	}, [tab])

	return (
		<div className="mt-6">
			<div className="flex space-x-6 mb-6">
				<button
					onClick={() => setActiveTab("talent")}
					className={cn(
						"pb-2",
						activeTab === "talent" ? "border-b-2 border-[#000] font-medium" : ""
					)}
				>
					Top talents
				</button>
				<button
					onClick={() => setActiveTab("feedback")}
					className={cn(
						"pb-2",
						activeTab === "feedback" ? "border-b-2 border-[#000] font-medium" : ""
					)}
				>
					Feedback
				</button>
			</div>

			<div className="relative mb-6">
				<Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-[#505050] h-4 w-4" />
				<Input
					placeholder="Search name"
					className="pl-10 bg-[#F7F7F7] border-none rounded-full max-w-md"
				/>
			</div>
			{/* Block table */}
			{activeTab === "talent" && (
				<div className="overflow-x-auto">
					<table className="w-full">
						<thead>
							<tr className="border-b border-gray-200 text-sm text-[#505050] opacity-60">
								<th className="text-left py-3 px-4 font-light">#</th>
								<th className="text-left py-3 px-4 font-light">Candidate Name</th>
								<th className="text-left py-3 px-4 font-light">Task</th>
								<th className="text-left py-3 px-4 font-light">Completion Time</th>
							</tr>
						</thead>
						<tbody className="divide-y divide-gray-100">
							{talents.map((talent) => (
								<tr key={talent.rank} className="hover:bg-gray-50">
									<td className="py-4 px-4 text-[#505050]">
										<span className="text-lg">{talent.medal}</span>
									</td>
									<td className="py-4 px-4">
										<div>
											{/* <div className="w-8 h-8 bg-gradient-to-r from-blue-400 to-purple-500 rounded-full mr-3"></div> */}
											<p className="font-medium">{talent.name}</p>
											<p className="font-light text-xs"><EMAIL></p>
										</div>
									</td>
									<td className="py-4 px-4 text-[#505050]">
										<span>{talent.task}</span>
									</td>
									<td className="py-4 px-4 text-[#505050]">
										<span>{talent.time}</span>
									</td>
								</tr>
							))}
						</tbody>
					</table>
				</div>
			)}
			{activeTab === "feedback" && (
				isLoading ? (
					<div className="flex justify-center items-center py-12">
						<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-loader-icon lucide-loader animate-spin"><path d="M12 2v4" /><path d="m16.2 7.8 2.9-2.9" /><path d="M18 12h4" /><path d="m16.2 16.2 2.9 2.9" /><path d="M12 18v4" /><path d="m4.9 19.1 2.9-2.9" /><path d="M2 12h4" /><path d="m4.9 4.9 2.9 2.9" /></svg>
					</div>
				) : feedback && feedback.length > 0 ? (
					<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
						{feedback.map((testimonial, index) => (
							<TestimonialCard
								key={index}
								name={testimonial?.userId?.name}
								avatar={testimonial?.userId?.avatar || "https://images.icon-icons.com/2859/PNG/512/avatar_face_man_boy_profile_smiley_happy_people_icon_181659.png"}
								rating={testimonial?.scores}
								testimonial={testimonial?.content}
							/>
						))}
					</div>
				) : (
					<div className="flex justify-center items-center py-12 text-sm text-gray-500 italic">
						No feedback available.
					</div>
				)
			)}

			{/* <div className="flex items-center justify-between mt-6">
				<div className="flex items-center space-x-2">
					<span className="text-sm text-gray-500">Rows per page</span>
					<select className="border border-gray-300 rounded px-2 py-1 text-sm w-[60px]">
						<option>10</option>
						<option>25</option>
						<option>50</option>
					</select>
				</div>
				<div className="flex items-center space-x-2">
					<Button size="sm" className="bg-transparent hover:bg-gray-50">
						<ChevronLeft color="black" />
					</Button>
					<Button variant="outline" size="sm" className="flex-1">
						1
					</Button>
					<Button variant="outline" size="sm" className="flex-1">
						2
					</Button>
					<Button size="sm" className="bg-transparent hover:bg-gray-50">
						<ChevronRight color="black" />
					</Button>
				</div>
			</div> */}
		</div>
	);
};

export default JobSimulationTalentsTable;