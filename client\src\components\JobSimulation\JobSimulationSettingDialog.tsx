import { mergeFileConfig } from 'librechat-data-provider';
import { RotateCw, Upload, X, ZoomIn, ZoomOut } from 'lucide-react';
import { useCallback, useEffect, useRef, useState } from 'react';
import AvatarEditor from 'react-avatar-editor';
import { useParams } from 'react-router-dom';
import {
  OGDialog,
  OGDialogContent,
  OGDialogFooter,
  OGDialogHeader,
  OGDialogTitle,
  Slider,
} from '~/components';
import { useGetFileConfig } from '~/data-provider';
import { useGetJobSimulationDataQuery } from '~/data-provider/JobSimulation';
import { useUpdateJobSimulationLogoMutation } from '~/data-provider/JobSimulation/mutations';
import { useToastContext } from '~/Providers';
import { cn, formatBytes } from '~/utils';

interface IJobSimulationSettingDialogProps {
  isOpenDialog: boolean;
  closeDialog: () => void;
  jobId?: string;
}

// Use the AvatarEditorRef from the type definition file

const JobSimulationSettingDialog = ({
  isOpenDialog,
  closeDialog,
  jobId,
}: IJobSimulationSettingDialogProps) => {
  const { jobSimulationId = '' } = useParams();
  const [errors, setErrors] = useState<string>('');
  const [logoFile, setLogoFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [isCropping, setIsCropping] = useState(false);
  const [scale, setScale] = useState<number>(1);
  const [rotation, setRotation] = useState<number>(0);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const dropAreaRef = useRef<HTMLDivElement>(null);
  const editorRef = useRef<AvatarEditor>(null);
  const { showToast } = useToastContext();

  const { data: fileConfigData } = useGetFileConfig();
  const fileConfig = mergeFileConfig(fileConfigData ?? undefined);

  const { data: jobSimulationData, refetch } = useGetJobSimulationDataQuery(
    jobId ?? jobSimulationId,
    'JobSimulationSettingDialog',
    { enabled: false },
  );

  const updateLogo = useUpdateJobSimulationLogoMutation({
    onSuccess: () => {
      showToast({ message: 'Logo updated successfully', status: 'success' });
      setIsUploading(false);
      refetch();
      closeDialog();
    },
    onError: (error) => {
      console.error('Error:', error);
      showToast({ message: 'Failed to update logo', status: 'error' });
      setIsUploading(false);
    },
  });

  useEffect(() => {
    // Clean up preview URL when component unmounts
    return () => {
      if (previewUrl) {
        URL.revokeObjectURL(previewUrl);
      }
    };
  }, [previewUrl]);

  const validateFile = (file: File): boolean => {
    // Check if file is an image
    if (!file.type.startsWith('image/')) {
      setErrors('Please upload an image file');
      return false;
    }

    // Check file size
    const sizeLimit = fileConfig.avatarSizeLimit ?? 0;
    if (sizeLimit && file.size > sizeLimit) {
      const megabytes = sizeLimit ? formatBytes(sizeLimit) : 2;
      setErrors(`File size should be less than ${megabytes}`);
      return false;
    }

    setErrors('');
    return true;
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    handleFile(file);
  };

  const handleFile = (file: File | undefined) => {
    if (!file) return;

    if (validateFile(file)) {
      setLogoFile(file);
      const preview = URL.createObjectURL(file);
      setPreviewUrl(preview);
      setIsCropping(true);
    }
  };

  const handleDrop = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    const file = e.dataTransfer.files[0];
    handleFile(file);
  }, []);

  const handleDragOver = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    if (dropAreaRef.current) {
      dropAreaRef.current.classList.add('border-blue-500', 'bg-blue-50');
    }
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    if (dropAreaRef.current) {
      dropAreaRef.current.classList.remove('border-blue-500', 'bg-blue-50');
    }
  }, []);

  const openFileDialog = () => {
    fileInputRef.current?.click();
  };

  const resetImage = () => {
    setLogoFile(null);
    setPreviewUrl(null);
    setIsCropping(false);
    setScale(1);
    setRotation(0);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleScaleChange = (value: number[]) => {
    setScale(value[0]);
  };

  const handleRotate = () => {
    setRotation((prevRotation) => (prevRotation + 90) % 360);
  };

  const handleFitToHexagon = () => {
    setScale(0.6);
  };

  const handleZoomIn = () => {
    setScale((prevScale) => Math.min(prevScale + 0.1, 3));
  };

  const handleZoomOut = () => {
    setScale((prevScale) => Math.max(prevScale - 0.1, 0.5));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!logoFile) {
      setErrors('Please select a logo image');
      return;
    }

    setIsUploading(true);

    if (editorRef.current && isCropping) {
      // Get the cropped canvas and convert to blob
      const canvas = editorRef.current.getImageScaledToCanvas();

      // Create a new canvas with exactly 240x240 dimensions
      const finalCanvas = document.createElement('canvas');
      finalCanvas.width = 240;
      finalCanvas.height = 240;
      const ctx = finalCanvas.getContext('2d');

      if (ctx) {
        // Draw the cropped image onto the 240x240 canvas
        ctx.drawImage(canvas, 0, 0, 240, 240);

        // Convert to blob and upload
        finalCanvas.toBlob((blob) => {
          if (blob) {
            const formData = new FormData();
            formData.append('file', blob, 'logo.png');
            formData.append('jobSimulationId', jobId ?? jobSimulationId);
            updateLogo.mutate(formData);
          } else {
            setIsUploading(false);
            setErrors('Failed to process the image');
          }
        }, 'image/png');
      } else {
        setIsUploading(false);
        setErrors('Failed to process the image');
      }
    } else {
      // Fallback to original file if not cropping
      const formData = new FormData();
      formData.append('file', logoFile);
      formData.append('jobSimulationId', jobId ?? jobSimulationId);
      updateLogo.mutate(formData);
    }
  };

  const handleCloseDialog = () => {
    resetImage();
    closeDialog();
  };

  return (
    <OGDialog open={isOpenDialog} onOpenChange={handleCloseDialog}>
      <OGDialogContent
        title={`Update Job Simulation`}
        className="max-w-[90vw] bg-background text-text-primary shadow-2xl sm:max-w-[600px] lg:max-w-[700px]"
      >
        <OGDialogHeader>
          <OGDialogTitle>Update Job Simulation Logo</OGDialogTitle>
        </OGDialogHeader>

        <div className="flex flex-col space-y-4 p-4">
          {/* Current logo preview */}
          {jobSimulationData?.logo && !previewUrl && (
            <div className="mb-4 flex flex-col items-center">
              <p className="mb-2 text-sm text-gray-600">Current Logo:</p>
              <img
                src={jobSimulationData.logo}
                alt="Current Logo"
                className="max-h-32 max-w-full rounded border object-contain"
              />
            </div>
          )}

          {/* Image cropping view */}
          {previewUrl && isCropping ? (
            <div className="flex flex-col items-center space-y-4">
              <div className="relative overflow-hidden rounded">
                <AvatarEditor
                  ref={editorRef}
                  image={previewUrl}
                  width={240}
                  height={240}
                  border={0}
                  color={[255, 255, 255, 0.6]}
                  scale={scale}
                  rotate={rotation}
                />
                {/* Hexagon overlay preview */}
                <div className="pointer-events-none absolute inset-0">
                  <svg width="240" height="240" className="absolute inset-0" viewBox="0 0 240 240">
                    <path
                      d="M7.2,120
                        C7.2,115.2 7.68,110.4 9.6,106.32
                        L55.2,25.68
                        C57.12,21.6 60.48,18 64.8,16.08
                        L175.2,16.08
                        C179.52,18 182.88,21.6 184.8,25.68
                        L230.4,106.32
                        C232.32,110.4 232.8,115.2 232.8,120
                        L232.8,120
                        C232.8,124.8 232.32,129.6 230.4,133.68
                        L184.8,214.32
                        C182.88,218.4 179.52,222 175.2,223.92
                        L64.8,223.92
                        C60.48,222 57.12,218.4 55.2,214.32
                        L9.6,133.68
                        C7.68,129.6 7.2,124.8 7.2,120Z"
                      fill="none"
                      stroke="#3b82f6"
                      strokeWidth="2"
                      strokeOpacity="0.7"
                    />
                  </svg>
                </div>
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    resetImage();
                  }}
                  className="absolute right-0 top-0 rounded-full bg-red-500/30 p-1 text-white hover:bg-red-500"
                >
                  <X size={16} />
                </button>
              </div>
              <div className="mt-4 flex w-full flex-col items-center space-y-4">
                {/* Preset buttons */}
                <div className="flex flex-wrap justify-center gap-2">
                  <button
                    onClick={handleFitToHexagon}
                    className="rounded bg-blue-500 px-3 py-1 text-xs text-white transition-colors hover:bg-blue-600"
                  >
                    Fit to Hexagon
                  </button>
                  <button
                    onClick={handleZoomOut}
                    className="flex items-center gap-1 rounded bg-gray-500 px-2 py-1 text-xs text-white transition-colors hover:bg-gray-600"
                  >
                    <ZoomOut size={12} />
                    Zoom Out
                  </button>
                  <button
                    onClick={handleZoomIn}
                    className="flex items-center gap-1 rounded bg-gray-500 px-2 py-1 text-xs text-white transition-colors hover:bg-gray-600"
                  >
                    <ZoomIn size={12} />
                    Zoom In
                  </button>
                  <button
                    onClick={handleRotate}
                    className="flex items-center gap-1 rounded bg-gray-500 px-2 py-1 text-xs text-white transition-colors hover:bg-gray-600"
                  >
                    <RotateCw size={12} />
                    Rotate
                  </button>
                </div>

                {/* Zoom slider */}
                <div className="flex w-full items-center justify-center space-x-4">
                  <span className="text-sm">Zoom</span>
                  <Slider
                    value={[scale]}
                    min={0.5}
                    max={3}
                    step={0.01}
                    onValueChange={handleScaleChange}
                    className="w-2/3 max-w-xs"
                  />
                  <span className="min-w-[3rem] text-xs text-gray-500">{scale.toFixed(2)}x</span>
                </div>
              </div>
              <div className="text-center">
                <p className="text-xs text-gray-500">The image will be cropped to 240px x 240px</p>
                <p className="mt-1 text-xs text-blue-500">Blue outline shows hexagon preview</p>
              </div>
            </div>
          ) : (
            /* Upload area */
            <div
              ref={dropAreaRef}
              className={cn(
                'flex min-h-[150px] cursor-pointer flex-col items-center justify-center rounded-lg border-2 border-dashed border-gray-300 bg-gray-50 p-6 transition-colors dark:border-gray-600 dark:bg-gray-700',
                previewUrl ? 'border-green-500' : '',
              )}
              onClick={openFileDialog}
              onDrop={handleDrop}
              onDragOver={handleDragOver}
              onDragLeave={handleDragLeave}
            >
              {previewUrl ? (
                <div className="relative flex w-full flex-col items-center">
                  <img
                    src={previewUrl}
                    alt="Logo Preview"
                    className="max-h-32 max-w-full rounded object-contain"
                  />
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      resetImage();
                    }}
                    className="absolute -right-2 -top-2 rounded-full bg-red-500 p-1 text-white hover:bg-red-600"
                  >
                    <X size={16} />
                  </button>
                </div>
              ) : (
                <>
                  <Upload className="mb-2 h-10 w-10 text-gray-400" />
                  <p className="mb-2 text-sm text-gray-500 dark:text-gray-400">
                    <span className="font-semibold">Click to upload</span> or drag and drop
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    PNG, JPG or GIF (Max {formatBytes(fileConfig.avatarSizeLimit ?? 0)})
                  </p>
                </>
              )}
              <input
                ref={fileInputRef}
                type="file"
                className="hidden"
                accept="image/*"
                onChange={handleFileChange}
              />
            </div>
          )}

          {/* Error message */}
          {errors && <div className="mt-2 text-sm text-red-500">{errors}</div>}
        </div>

        <OGDialogFooter>
          <div className="flex gap-2">
            <button
              onClick={handleCloseDialog}
              className="mt-3 block w-fit rounded bg-gray-100 px-4 py-2 text-base font-semibold text-neutral-900 hover:opacity-90"
              disabled={isUploading}
            >
              Cancel
            </button>
            <button
              onClick={handleSubmit}
              className={cn(
                'mt-3 block w-fit rounded px-4 py-2 text-base font-semibold text-white hover:opacity-90',
                isUploading ? 'cursor-not-allowed bg-gray-500' : 'bg-neutral-900',
              )}
              disabled={isUploading || !logoFile}
            >
              {isUploading ? 'Uploading...' : 'Save'}
            </button>
          </div>
        </OGDialogFooter>
      </OGDialogContent>
    </OGDialog>
  );
};

export default JobSimulationSettingDialog;
