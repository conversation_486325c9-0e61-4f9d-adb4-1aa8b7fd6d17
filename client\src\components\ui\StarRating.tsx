import React from 'react';

interface StarRatingProps {
  value: number;
  onChange: (value: number) => void;
}

const StarRating = ({ value = 0, onChange }: StarRatingProps) => {
  const totalStars = 5;

  return (
    <div className="flex space-x-1">
      {Array.from({ length: totalStars }, (_, index) => {
        const isFilled = index < value;

        return (
          <svg
            key={index}
            onClick={() => onChange(index + 1)}
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 20 20"
            fill={isFilled ? '#34A1F4' : 'none'}
            stroke={isFilled ? '#34A1F4' : '#D1D5DB'}
            strokeWidth={1}
            className="h-8 w-8 cursor-pointer transition-transform hover:scale-110"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.538 1.118l-2.8-2.034a1 1 0 00-1.176 0l-2.8 2.034c-.783.57-1.838-.197-1.538-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.462a1 1 0 00.95-.69l1.07-3.292z"
            />
          </svg>
        );
      })}
    </div>
  );
};

export default StarRating;
