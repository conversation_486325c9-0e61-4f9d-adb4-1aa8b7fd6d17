import { useState, isValidElement, cloneElement, useEffect } from 'react';
import { Button, Dialog, Input, OGDialogTemplate, Textarea, StarRating } from '~/components/ui';
import { dataService } from 'librechat-data-provider';
import type t from 'librechat-data-provider';
import { useRecoilValue } from 'recoil';
import store from '~/store';
import { useToastContext } from '~/Providers';
import { useParams } from 'react-router-dom';

interface FeedbackDialogProps {
  button?: React.ReactNode;
  jobId?: string;
}

export default function FeedbackDialog({ button, jobId }: FeedbackDialogProps) {
  const [open, setOpen] = useState<boolean>(false);
  const [rating, setRating] = useState<number>(0);
  const [content, setContent] = useState<string>('');
  const [feedback, setFeedback] = useState<t.TJobSimulationFeedback | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [feedbackSuccess, setFeedbackSuccess] = useState<boolean>(false);

  const user = useRecoilValue(store.user);
  const { showToast } = useToastContext();
  const { jobSimulationId: jobSimulationIdUrl } = useParams();

  const jobSimulationId = (jobId || jobSimulationIdUrl) as string;

  const onClose = () => {
    setOpen(false);
    setFeedbackSuccess(false);
  };
  const onOpen = () => {
    handleGetFeedbackByUserAndJob();
    setTimeout(() => setOpen(true), 100);
  };

  const handleGetFeedbackByUserAndJob = async () => {
    setIsLoading(true);
    try {
      const feedback = await dataService.getJobSimulationFeedbackByUserAndJob(
        user?.id as string,
        jobSimulationId,
      );
      setFeedback(feedback);
      setContent(feedback?.content || '');
      setRating(feedback?.scores || 0);
    } catch (error) {}
    setIsLoading(false);
  };

  const handleFeedback = async () => {
    setIsLoading(true);
    try {
      if (feedback) {
        const updatedFeedback = await dataService.updateJobSimulationFeedback(
          feedback._id as string,
          {
            userId: user?.id as string,
            scores: rating,
            content: content,
          },
        );
        setFeedback(updatedFeedback);
        setContent(updatedFeedback?.content || '');
        setRating(updatedFeedback?.scores || 0);
        if (feedback) {
          setFeedbackSuccess(true);
        } else {
          showToast({
            message: 'Failed to update feedback',
            status: 'error',
          });
        }
      } else {
        const newFeedback = await dataService.createJobSimulationFeedback({
          jobSimulationId: jobSimulationId,
          userId: user?.id as string,
          scores: rating,
          content: content,
        });
        setFeedback(newFeedback);
        setContent(newFeedback?.content || '');
        setRating(newFeedback?.scores || 0);
        if (newFeedback) {
          setFeedbackSuccess(true);
        } else {
          showToast({
            message: 'Failed to send feedback',
            status: 'error',
          });
        }
      }
    } catch (error) {
      if (feedback) {
        showToast({
          message: 'Failed to update feedback',
          status: 'error',
        });
      } else {
        showToast({
          message: 'Failed to send feedback',
          status: 'error',
        });
      }
    }
    setIsLoading(false);
  };

  const handleRemoveFeedback = async () => {
    setIsLoading(true);
    try {
      const deletedFeedback = await dataService.deleteJobSimulationFeedback(
        feedback?._id as string,
        {
          userId: user?.id as string,
        },
      );
      setFeedback(null);
      setContent('');
      setRating(0);
      if (deletedFeedback) {
        showToast({
          message: 'Feedback removed successfully',
          status: 'success',
        });
        onClose();
      } else {
        showToast({
          message: 'Failed to remove feedback',
          status: 'error',
        });
      }
    } catch (error) {
      showToast({
        message: 'Failed to remove feedback',
        status: 'error',
      });
    }
    setIsLoading(false);
  };

  const renderButton = () => {
    if (button && isValidElement(button)) {
      return cloneElement(button as React.ReactElement, {
        onClick: (e: any) => {
          button.props?.onClick?.(e);
          onOpen();
        },
      });
    }

    return (
      <i className="flex items-center hover:cursor-pointer dark:text-white" onClick={onOpen}>
        <u>Send feedback</u>
      </i>
    );
  };

  return (
    <>
      {renderButton()}

      {open && (
        <Dialog open={open} onOpenChange={onClose}>
          <OGDialogTemplate
            className="w-full max-w-xl"
            title=""
            main={
              <div className="flex flex-col gap-5">
                {feedbackSuccess ? (
                  <>
                    <div className="flex justify-center">
                      <img src={'/assets/stars.png'} alt="stars" />
                    </div>
                    <div className="text-center text-[16px]">
                      Thanks for your feedback, we appreciate your experiences and suggestions. This
                      helps us improve our products.
                    </div>
                  </>
                ) : (
                  <>
                    <div className="text-center text-[30px]">Send us some feedback!</div>
                    <div className="text-center text-[16px]">
                      Thank you very much for using and accompanying us, can you suggest or feedback
                      your experiences when using our features? This helps me improve the experience
                      and develop to help more people.
                    </div>
                    <div className="flex flex-col items-center gap-2">
                      <div>Rating:</div>
                      <StarRating value={rating} onChange={setRating} />
                    </div>
                    <Textarea
                      value={content}
                      onChange={(e) => setContent(e.target.value)}
                      placeholder="Write your experiences or suggestions for improvement here..."
                      rows={6}
                      maxLength={500}
                      className="resize-none"
                    />
                    <div className="mt-4 flex flex-col gap-4">
                      <Button className="w-full" onClick={handleFeedback} disabled={isLoading} variant="job">
                        {feedback ? 'Update' : 'Send'} Feedback
                      </Button>
                      {feedback && (
                        <Button
                          className="w-full"
                          variant="outline"
                          onClick={handleRemoveFeedback}
                          disabled={isLoading}
                        >
                          Remove Feedback
                        </Button>
                      )}
                    </div>
                  </>
                )}
              </div>
            }
            showCancelButton={false}
          />
        </Dialog>
      )}
    </>
  );
}
