import * as Ariakit from '@ariakit/react';
import { motion } from 'framer-motion';
import { TStartupConfig } from 'librechat-data-provider';
import React, { useState } from 'react';
import { useSetRecoilState } from 'recoil';
import type * as t from '~/common';
import { DropdownPopup, ThemeSelector } from '~/components/ui';
import { TranslationKeys, useLocalize, useMediaQuery } from '~/hooks';
import store from '~/store';
import { cn } from '~/utils';
import { Banner } from '../Banners';
import Footer from './Footer';
import SocialLoginRender from './SocialLoginRender';

const ErrorRender = ({ children }: { children: React.ReactNode }) => (
  <div className="mb-10 mt-10 flex justify-center">
    <div
      role="alert"
      aria-live="assertive"
      className="rounded-md border border-red-500 bg-red-500/10 px-3 py-2 text-sm text-gray-600 dark:text-gray-200"
    >
      {children}
    </div>
  </div>
);

function DemoAccountDropdown({ className }: { className?: string }) {
  const [isPopoverActive, setIsPopoverActive] = useState(false);
  const isSmallScreen = useMediaQuery('(max-width: 768px)');
  const menuId = 'demo-account-profile-menu';

  const setJobsimulationDemoAccount = useSetRecoilState(store.jobsimulationDemoAccount);

  const dropdownItems: t.MenuItemProps[] = [
    {
      label: 'Candidate',
      onClick: () => setJobsimulationDemoAccount('user'),
      // icon: <LogOut className="icon-md mr-2 text-text-secondary" />,
      hideOnClick: true,
    },
    {
      label: 'Employer',
      onClick: () => setJobsimulationDemoAccount('employer'),
      // icon: <LogOut className="icon-md mr-2 text-text-secondary" />,
      hideOnClick: true,
    },
  ];

  return (
    <DropdownPopup
      menuId={menuId}
      focusLoop={true}
      isOpen={isPopoverActive}
      setIsOpen={setIsPopoverActive}
      trigger={
        <Ariakit.MenuButton
          id="user-profile-button"
          aria-label="User profile options"
          className={cn(
            'flex items-center gap-2 rounded-lg bg-white p-2 text-text-primary transition-all ease-in-out hover:bg-surface-tertiary dark:bg-gray-700 dark:text-white',
            className,
          )}
        >
          <span className="hidden text-sm md:block">Demo Account</span>
        </Ariakit.MenuButton>
      }
      items={dropdownItems}
      className={isSmallScreen ? '' : 'absolute bottom-0 left-0'}
    />
  );
}

function AuthLayout({
  children,
  header,
  headerLogo,
  isFetching,
  startupConfig,
  startupConfigError,
  pathname,
  error,
  role,
}: {
  children: React.ReactNode;
  header: React.ReactNode;
  headerLogo?: string;
  isFetching: boolean;
  startupConfig: TStartupConfig | null | undefined;
  startupConfigError: unknown | null | undefined;
  pathname: string;
  error: TranslationKeys | null;
  role: string | null;
}) {
  const localize = useLocalize();

  const hasStartupConfigError = startupConfigError !== null && startupConfigError !== undefined;
  const DisplayError = () => {
    if (hasStartupConfigError) {
      return <ErrorRender>{localize('com_auth_error_login_server')}</ErrorRender>;
    } else if (error === 'com_auth_error_invalid_reset_token') {
      return (
        <ErrorRender>
          {localize('com_auth_error_invalid_reset_token')}{' '}
          <a className="font-semibold text-green-600 hover:underline" href="/forgot-password">
            {localize('com_auth_click_here')}
          </a>{' '}
          {localize('com_auth_to_try_again')}
        </ErrorRender>
      );
    } else if (error != null && error) {
      return <ErrorRender>{localize(error)}</ErrorRender>;
    }
    return null;
  };

  return (
    <div className="relative flex min-h-screen flex-col items-center justify-center overflow-hidden bg-white px-4 dark:bg-gray-900 sm:px-8">
      <Banner />
      <DisplayError />
      <div className="absolute bottom-0 left-0 md:m-4">
        <ThemeSelector />
        {pathname === '/login' && (
          <DemoAccountDropdown className="absolute bottom-0 left-16 m-4 w-[130px] rounded-lg p-2 transition-colors hover:bg-surface-hover focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2" />
        )}
      </div>

      <div className="z-10 flex w-full max-w-6xl flex-col items-center justify-between gap-12 sm:flex-row">
        {/* FORM SECTION */}
        <motion.div
          initial={{ opacity: 0, x: -100, scale: 0.95 }}
          animate={{ opacity: 1, x: 0, scale: 1 }}
          transition={{ type: 'spring', stiffness: 40, damping: 18, delay: 0.1 }}
          className="min-h-[380px] w-full space-y-6 rounded-2xl bg-white px-8 py-10 dark:bg-gray-800"
        >
          <h1 className="text-center text-3xl font-bold text-gray-900 dark:text-white">
            Welcome to{' '}
            <span className="text-black dark:text-white">
              {role === 'employer' ? 'Employer Portal' : 'Job Simulation'}
            </span>
          </h1>
          {role === 'employer' && (
            <p className="!mt-1 text-center text-lg font-light italic text-gray-700 dark:text-white">
              Your gateway to hiring smarter with immersive job simulations.
            </p>
          )}
          {children}
          {!pathname.includes('2fa') &&
            (pathname.includes('login') || pathname.includes('register')) && (
              <SocialLoginRender startupConfig={startupConfig} />
            )}
        </motion.div>

        {/* ILLUSTRATION SECTION */}
        <motion.div
          initial={{ opacity: 0, y: 60 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3, duration: 0.8, ease: 'easeOut' }}
          className="order-first w-full sm:order-last sm:max-w-2xl"
        >
          <img
            src={headerLogo}
            alt={localize('com_ui_logo', { 0: startupConfig?.appTitle ?? 'Job Simulation' })}
            className="h-auto w-full object-contain drop-shadow-xl"
          />
        </motion.div>
      </div>
      <Footer startupConfig={startupConfig} />
    </div>
  );
}

export default AuthLayout;
