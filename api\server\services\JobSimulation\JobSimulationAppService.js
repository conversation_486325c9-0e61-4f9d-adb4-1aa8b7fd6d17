const { uploadFileToS3 } = require('~/server/services/Files/S3/crud');

const { jobSimulationApp, getUserById } = require('~/models');

const JobSimulationAppService = {
  async create(req) {
    const { body, file } = req;

    try {
      const user = await getUserById(body.userId);
      if (!user) {
        throw new Error('User not found');
      }
      if (!file) {
        throw new Error('File is required for creating a job simulation app');
      }
      const uploadResult = await uploadFileToS3({ req, file, file_id: Date.now() });
      if (!uploadResult?.filepath) return null;

      const appData = {
        ...body,
        icon: uploadResult.filepath,
      };

      const app = await jobSimulationApp.create(appData);
      return app;
    } catch (error) {
      throw new Error(`Failed to create job simulation app: ${error.message}`);
    }
  },

  async update(req) {
    const { body, file, params } = req;
    try {
      const updateData = { ...body };
      if (file) {
        const uploadResult = await uploadFileToS3({ req, file, file_id: Date.now() });
        if (!uploadResult?.filepath) return null;
        updateData.icon = uploadResult.filepath;
      }

      delete updateData.type;
      delete updateData.role;

      const app = await jobSimulationApp.update(params.id, updateData);
      return app;
    } catch (error) {
      throw new Error(`Failed to update job simulation app: ${error.message}`);
    }
  },

  async deleteById(id, params) {
    try {
      const app = await jobSimulationApp.deleteById(id, params);
      return app;
    } catch (error) {
      throw new Error(`Failed to delete job simulation app: ${error.message}`);
    }
  },

  async getAll() {
    try {
      return await jobSimulationApp.getAll();
    } catch (error) {
      throw new Error(`Failed to retrieve job simulation apps: ${error.message}`);
    }
  },

  async getById(id) {
    try {
      return await jobSimulationApp.getById(id);
    } catch (error) {
      throw new Error(`Failed to retrieve job simulation app: ${error.message}`);
    }
  },

  async getByIds(ids) {
    try {
      return await jobSimulationApp.getByIds(ids);
    } catch (error) {
      throw new Error(`Failed to retrieve job simulation apps by IDs: ${error.message}`);
    }
  },
};

module.exports = JobSimulationAppService;
