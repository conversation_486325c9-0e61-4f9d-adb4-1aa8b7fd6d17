const feedbackMessages = {
    passed: [
        "Congratulations! Your submission met all the key criteria, though there's still room for improvement:",
        "Your submission has been marked as passed — well done! Please consider the following feedback:",
        "Solid work! Your task was spot on.",
        "Excellent submission!",
        "You've successfully completed the task! Here are a few pointers for future improvements:",
        "Everything checks out perfectly — great work!",
        "Good news: your submission passed. Below are some optional enhancements to consider:",
        "Impressive work! Keep it up.",
        "Your answer is correct — well done!",
        "Task completed successfully — here's some feedback for refinement:"
    ],
    failed: [
        "Unfortunately, your submission did not fully meet the required expectations. Here are the areas for improvement:",
        "Your submission was reviewed, but it didn't meet all the necessary criteria. Please see the feedback below:",
        "Thanks for submitting. However, some key elements were missing. Please review the points below:",
        "After reviewing your submission, we found several required components were not addressed:",
        "We appreciate your effort, but the submission did not satisfy all task requirements. Here's what needs to be improved:",
        "The current submission falls short of the expected standards. See the issues identified below:",
        "Your submission was evaluated and unfortunately did not pass. These are the areas that require attention:",
        "Thank you for your work, but there are a few critical gaps. See below for specific feedback:",
        "A good attempt! Just needs a few tweaks:",
    ],
    default: [
        "Thanks for your submission.",
        "Appreciate your effort on this task.",
        "Submission received — thank you!",
        "Got your submission! We'll review it shortly.",
        "Thanks! We'll take a look and get back to you."
    ]
};

const emailFooters = {
    passed: ['Keep up the good work, and feel free to reach out if you have any questions.'],
    failed: ['Please review the feedback above and consider revising your submission. Let us know if you have any questions or need further clarification.']
}

const getRandomFeedback = (status, feedback = "") => {
    let messages;
    let footers;

    if (status === 'passed' || status === 'failed') {
        messages = feedbackMessages[status];
        footers = emailFooters[status];
    } else {
        messages = feedbackMessages.default;
        footers = [];
    }

    const randomMsgIndex = Math.floor(Math.random() * messages.length);
    const randomFooterIndex = Math.floor(Math.random() * footers.length);
    const baseMessage = messages[randomMsgIndex];

    if (feedback.trim()) {
        return `${baseMessage}\n\n${feedback}\n\n${footers[randomFooterIndex]}`;
    } else {
        return baseMessage;
    }
}

module.exports = {
    getRandomFeedback
};