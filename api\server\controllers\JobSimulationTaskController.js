const JobSimulationService = require('~/server/services/JobSimulation/JobSimulationService');
const JobSimulationInteractionService = require('~/server/services/JobSimulation/JobSimulationInteractionService');
const BillionService = require('~/server/services/Billion/BillionService');
const { getRandomFeedback } = require('~/server/utils/jobSimulation/taskSubmissionFeedback');
// const { logger } = require('~/config');

const JobSimulationTaskController = {

  async getAvailableTask(req, res) {
    try {
      const jobSimulation = await JobSimulationService.getAdminJobSimulation(req.params.jobSimulationId);
      if (!jobSimulation) {
        throw new Error('Job simulation not found');
      }
      const tasks = await BillionService.getTasks({ email: req.user.email, billionIntakeId: jobSimulation.billionIntakeId });
      if (tasks?.length) {
        // Save tasks to user interaction
        await JobSimulationInteractionService.saveUserInteraction({
          jobSimulationId: req.params.jobSimulationId,
          jobSimulationEmail: req.user.email,
          interaction: {
            type: 'update-tasks',
            tasks,
          },
        });
      }
      const availableTask = (tasks || []).find(task => task.status !== 'passed') || {};
      JobSimulationService.updateProgressTasks(req.params.jobSimulationId, req.user.email);
      res.json({
        jobSimulation,
        task: availableTask,
      });
    } catch (error) {
      console.error('=== ERROR RETRIEVING TASKS ===', error);
      res.status(500).json({ error: 'Failed to get tasks' });
    }
  },

  async submitTask(req, res) {
    try {
      const jobSimulation = await JobSimulationService.getAdminJobSimulation(req.params.jobSimulationId);
      if (!jobSimulation) {
        throw new Error('Job simulation not found');
      }
      // Save progress email
      const progress = await JobSimulationService.updateProgressEmailReplies(req.params.jobSimulationId, req.user.email, req.body.emailId, {
        id: req.body.replyId,
        isUser: true,
        datetime: Date.now(),
        content: req.body.content,
      });
      const resultTaskSubmission = await BillionService.submitTask({ email: req.user.email, taskId: req.body.taskId, content: req.body.content, billionIntakeId: jobSimulation.billionIntakeId });
      // Save user interaction `tasks`

      let feedback = '';
      if (Array.isArray(resultTaskSubmission?.feedback)) {
        feedback = responseFeedback.join('\n');
      } else if (resultTaskSubmission.feedback) {
        feedback = String(resultTaskSubmission.feedback);
      }

      feedback = getRandomFeedback(resultTaskSubmission.status, feedback);

      if (resultTaskSubmission && resultTaskSubmission.status === 'passed') {
        await JobSimulationInteractionService.saveUserInteraction({
          jobSimulationId: req.params.jobSimulationId,
          jobSimulationEmail: req.user.email,
          interaction: {
            type: 'update-task',
            task: {
              taskId: req.body.taskId,
              status: 'passed',
            },
          },
        });
      }
      resultTaskSubmission.feedback = feedback;
      const email = progress.emails.find(email => email.id === req.body.emailId);
      if (email) {
        await JobSimulationService.updateProgressEmailReplies(req.params.jobSimulationId, req.user.email, req.body.emailId, {
          id: `reply-${(email.replies?.length || 1) + 1}:email-task:${req.body.taskId}`,
          isUser: false,
          datetime: Date.now(),
          content: feedback,
        });
      }
      try {
        await JobSimulationService.updateProgressTasks(req.params.jobSimulationId, req.user.email);
      } catch (error) {
      }
      res.json(resultTaskSubmission);
    } catch (error) {
      console.error('=== ERROR RETRIEVING TASKS ===', error);
      res.status(500).json({ error: 'Failed to submit tasks' });
    }
  },

  async updateProgressTasks(req, res) {
    try {
      await JobSimulationService.updateProgressTasks(req.params.jobSimulationId, req.user.email);
      res.json({
        jobSimulationId: req.params.jobSimulationId,
      });
    } catch (error) {
      console.error('=== ERROR UPDATING PROGRESS TASKS ===', error);
      res.status(500).json({ error: 'Failed to get tasks' });
    }
  },
};

module.exports = JobSimulationTaskController;