import { useOutletContext, useSearchParams } from 'react-router-dom';
import { useEffect, useState } from 'react';
import { useAuthContext } from '~/hooks/AuthContext';
import type { TLoginLayoutContext } from '~/common';
import { ErrorMessage } from '~/components/Auth/ErrorMessage';
import { getLoginError } from '~/utils';
import { useLocalize } from '~/hooks';
import LoginForm from './LoginForm';
import SocialButton from '~/components/Auth/SocialButton';
import { OpenIDIcon } from '~/components';
import { useRecoilState } from 'recoil';
import store from '~/store';

function Login() {
  const localize = useLocalize();
  const { error, setError, login } = useAuthContext();
  const { startupConfig, jobSimulationId, role } = useOutletContext<TLoginLayoutContext>();
  // const [jobSimulationId, setJobSimulationId] = useState<string | null>(null);
  // const [role, setRole] = useState<string | null>(null);

  const [searchParams, setSearchParams] = useSearchParams();
  // Determine if auto-redirect should be disabled based on the URL parameter
  const disableAutoRedirect = searchParams.get('redirect') === 'false';

  // Persist the disable flag locally so that once detected, auto-redirect stays disabled.
  const [isAutoRedirectDisabled, setIsAutoRedirectDisabled] = useState(disableAutoRedirect);

  const [jobsimulationDemoAccount, setJobsimulationDemoAccount] = useRecoilState(
    store.jobsimulationDemoAccount,
  );

  // useEffect(() => {
  //   const jobSimulationIdParam = searchParams.get('jobSimulationId');
  //   // const roleParam = searchParams.get('role');
  //   if (jobSimulationIdParam) {
  //     setJobSimulationId(jobSimulationIdParam);
  //   }
  //   // if (roleParam) {
  //   //   setRole(roleParam);
  //   // }
  // }, [searchParams]);

  // Once the disable flag is detected, update local state and remove the parameter from the URL.
  useEffect(() => {
    if (disableAutoRedirect) {
      setIsAutoRedirectDisabled(true);
      const newParams = new URLSearchParams(searchParams);
      newParams.delete('redirect');
      setSearchParams(newParams, { replace: true });
    }
  }, [disableAutoRedirect, searchParams, setSearchParams]);

  // Determine whether we should auto-redirect to OpenID.
  const shouldAutoRedirect =
    startupConfig?.openidLoginEnabled &&
    startupConfig?.openidAutoRedirect &&
    startupConfig?.serverDomain &&
    !isAutoRedirectDisabled;

  useEffect(() => {
    if (shouldAutoRedirect) {
      console.log('Auto-redirecting to OpenID provider...');
      window.location.href = `${startupConfig.serverDomain}/oauth/openid`;
    }
  }, [shouldAutoRedirect, startupConfig]);

  useEffect(() => {
    if (!jobsimulationDemoAccount) return;
    setJobsimulationDemoAccount("");
    if (jobsimulationDemoAccount === 'employer') {
      login({
        email: '<EMAIL>',
        password: '*********',
      });
    } else {
      login({
        email: '<EMAIL>',
        password: '*********',
      });
    }
  }, [jobsimulationDemoAccount, role]);

  // Render fallback UI if auto-redirect is active.
  if (shouldAutoRedirect) {
    return (
      <div className="flex min-h-screen flex-col items-center justify-center p-4">
        <p className="text-lg font-semibold">
          {localize('com_ui_redirecting_to_provider', { 0: startupConfig.openidLabel })}
        </p>
        <div className="mt-4">
          <SocialButton
            key="openid"
            enabled={startupConfig.openidLoginEnabled}
            serverDomain={startupConfig.serverDomain}
            oauthPath="openid"
            Icon={() =>
              startupConfig.openidImageUrl ? (
                <img src={startupConfig.openidImageUrl} alt="OpenID Logo" className="h-5 w-5" />
              ) : (
                <OpenIDIcon />
              )
            }
            label={startupConfig.openidLabel}
            id="openid"
          />
        </div>
      </div>
    );
  }

  return (
    <>
      {error != null && <ErrorMessage>{localize(getLoginError(error))}</ErrorMessage>}
      <LoginForm onSubmit={login} error={error} setError={setError} />
      <p className="!mt-1 text-center font-light text-gray-700 dark:text-white">
        {role === 'employer' ? (
          <>
            or
            <a
              href="/login"
              className="inline-flex p-1 font-medium text-job-primary-500 underline transition-colors hover:text-job-primary"
            >
              Sign in as Candidate
            </a>
          </>
        ) : (
          <>
            or
            <a
              href="/login?role=employer"
              className="inline-flex p-1 font-medium text-job-primary-500 underline transition-colors hover:text-job-primary"
            >
              Sign in as Employer
            </a>
          </>
        )}
      </p>
      {startupConfig?.registrationEnabled === true && (
        <p className="my-4 text-center font-light text-gray-700 dark:text-white">
          {localize('com_auth_no_account')}{' '}
          <a
            href={jobSimulationId ? `/register?jobSimulationId=${jobSimulationId}` : '/register'}
            className="inline-flex p-1 font-medium text-job-primary-500 underline transition-colors hover:text-job-primary"
          >
            {localize('com_auth_sign_up')}
          </a>
        </p>
      )}
    </>
  );
}

export default Login;
