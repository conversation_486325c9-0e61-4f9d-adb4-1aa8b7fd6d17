import { memo, useEffect, useRef, useState } from 'react';
import { useRecoilState, useRecoilValue, useSetRecoilState } from 'recoil';
import store from '~/store';
import BrowserStream from './BrowserStream/BrowserStream';


const VirtualBrowser = ({ url }: { url: string }) => {
  const wsRef = useRef<WebSocket | null>(null);
  const [isReady, setIsReady] = useState(false);
  const browserGoal = useRecoilValue(store.jobSimulationBrowserGoal);
  const setJobSimulationTriggerMessage = useSetRecoilState(store.jobSimulationTriggerMessage);
  const user = useRecoilValue(store.user);

  // TODO: temporary fix
  const wsUrl = import.meta.env.VITE_BROWSER_WS_URL || 'wss://dev-browser-api.agentos.cloud/ws';
  console.log("Check env ws url ::: ", import.meta.env.VITE_BROWSER_WS_URL);

  const handleReceiveAgentMessage = (msg: string) => {
    if (!msg?.trim()) return;
    setJobSimulationTriggerMessage({
      message: msg,
      isTriggered: true,
      isCustom: true,
      isUser: false,
    });
  };

  const handleReceiveStatusCode = (code: string) => {
    if (code === 'running') {
      setIsReady(true);
    } else if (code === 'closed') {
      setIsReady(false);
    }
  };

  // const handleSendInstruction = (instruction: string) => {
  //   if (wsRef.current?.readyState === WebSocket.OPEN && !!instruction.trim() && isReady) {
  //     wsRef.current.send(JSON.stringify({
  //       type: 'agent_instruction',
  //       data: {
  //         instruction
  //       }
  //     }));
  //   }
  // };

  // useEffect(() => {
  //   if (!virtualBrowserMessage) return;
  //   handleSendInstruction(virtualBrowserMessage);
  //   setVirtualBrowserMessage(null);
  // }, [virtualBrowserMessage]);

  return (
    <BrowserStream browserUrl={url} wsRef={wsRef} goal={browserGoal} wsUrl={`${wsUrl}?userId=${user!.id}`} onReceiveAgentMessage={handleReceiveAgentMessage} onReceiveStatusCode={handleReceiveStatusCode} />
  );
}

export default memo(VirtualBrowser);
