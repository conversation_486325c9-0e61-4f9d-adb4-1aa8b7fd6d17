const { JobSimulation, JobSimulationProgress, User, jobSimulationFeedback } = require('~/models');
const { startOfToday, startOfDay, endOfDay, startOfWeek, endOfWeek, startOfMonth, endOfMonth, subDays, format, addDays, subMonths } = require('date-fns');

const getDateRange = (data) => {
  const { timeType = 'today', dateFrom, dateTo } = data;
  // Use date-fns, parse to UTC date
  const startUTCDate = startOfToday({ in: 'UTC' });
  if (timeType === 'all') {
    return {};
  }
  if (timeType === 'last3months') {
    return {
      dateFrom: startOfMonth(subMonths(startUTCDate, 3), { in: 'UTC' }),
      dateTo: addDays(startUTCDate, 1, { in: 'UTC' }),
    };
  }
  if (dateFrom && dateTo && dateFrom === dateTo) {
    const df = startOfDay(new Date(dateFrom), { in: 'UTC' });
    return {
      dateFrom: df,
      dateTo: addDays(df, 1, { in: 'UTC' }),
    };
  }
  if (timeType === 'custom') {
    // TODO: check if range > 30 date --> throw error. or auto set enddate = today
    // Check if enddate <= today (UTC)
    return {
      dateFrom: startOfDay(new Date(dateFrom), { in: 'UTC' }),
      dateTo: endOfDay(new Date(dateTo), { in: 'UTC' }),
    };
  }
  if (timeType === 'yesterday') {
    return {
      dateFrom: startOfDay(subDays(startUTCDate, 1), { in: 'UTC' }),
      dateTo: startUTCDate,
    };
  }
  if (timeType === 'today') {
    return {
      dateFrom: startOfToday({ in: 'UTC' }),
      dateTo: addDays(startUTCDate, 1, { in: 'UTC' }),
    };
  }
  if (timeType === 'week') {
    return {
      dateFrom: startOfWeek(startUTCDate, { in: 'UTC', weekStartsOn: 1 }),
      dateTo: startOfDay(addDays(endOfWeek(startUTCDate, { in: 'UTC', weekStartsOn: 1 }), 1, { in: 'UTC' }), { in: 'UTC' }),
    };
  }
  if (timeType === 'month') {
    return {
      dateFrom: startOfMonth(startUTCDate, { in: 'UTC' }),
      dateTo: startOfDay(addDays(endOfMonth(startUTCDate, { in: 'UTC' }), 1, { in: 'UTC' }), { in: 'UTC' }),
    };
  }

  throw new Error('Invalid time type');
}

const getTimeLabels = (timeType = 'today', dateFrom, dateTo) => {
  if (['today', 'yesterday', 'day'].includes(timeType)) {
    return Array.from({ length: 24 }, (_, i) => `${i < 10 ? `0${i}` : i}`);
  }

  if (dateFrom && dateTo) {
    // Create array contains date string between dateFrom and dateTo (yyyy-mm-dd) in UTC
    const dateArray = [];
    let currentDate = dateFrom;
    while (currentDate < dateTo) {
      // format in UTC date
      dateArray.push(format(currentDate, 'yyyy-MM-dd', { in: 'UTC' }));
      currentDate = addDays(currentDate, 1, { in: 'UTC' });
    }
    return dateArray;
  }

}

const JobSimulationStatisticService = {

  async getEmployerOverview(fromDate, toDate) {
    // TODO: get public jobs
    const filterDate = fromDate && toDate ? {
      createdAt: {
        $gte: fromDate,
        $lte: toDate,
      }
    } : null;

    const filterProgresses = {
      $match: {
        jobStatus: 'public',
        ...(filterDate ? filterDate : {}),
      }
    };

    const totalJobSimsPromise = JobSimulation.model.countDocuments({});
    const progressResultsPromise = JobSimulationProgress.model.aggregate([
      filterProgresses,
      {
        $facet: {
          counts: [
            {
              $group: {
                _id: null,
                totalAttempts: { $sum: 1 },
                totalCompletedJobs: { $sum: { $cond: [{ $eq: ["$status", 'completed'] }, 1, 0] } },
                totalReferenceLetters: { $sum: { $cond: [{ $eq: ["$claimedCertification", true] }, 1, 0] } }
              }
            }
          ],
          totalParticipants: [
            {
              $group: {
                _id: "$email",
              }
            },
            {
              $count: "totalParticipants"
            }
          ]
        }
      },
      {
        $project: {
          totalAttempts: { $arrayElemAt: ["$counts.totalAttempts", 0] },
          totalCompletedJobs: { $arrayElemAt: ["$counts.totalCompletedJobs", 0] },
          totalReferenceLetters: { $arrayElemAt: ["$counts.totalReferenceLetters", 0] },
          totalParticipants: { $ifNull: [{ $arrayElemAt: ["$totalParticipants.totalParticipants", 0] }, 0] }
        }
      }
    ]);

    const [totalJobSims, progressResults] = await Promise.all([totalJobSimsPromise, progressResultsPromise]);

    return {
      totalJobs: totalJobSims,
      totalAttempts: progressResults?.[0].totalAttempts || 0,
      totalCompletedJobs: progressResults?.[0].totalCompletedJobs || 0,
      avgCompletionRate: Math.round(((progressResults?.[0].totalCompletedJobs || 0) / (progressResults?.[0].totalAttempts || 0)) * 100),
      totalReferenceLetters: progressResults?.[0].totalReferenceLetters || 0,
      totalParticipants: progressResults?.[0].totalParticipants || 0,
    };
  },

  async getTopPerformingJobs(data) {
    let { limit = 3 } = data || {};
    limit = Number.parseInt(limit);
    if (limit < 3 || limit > 5) limit = 3;
    const { dateFrom, dateTo } = getDateRange(data);
    let { timeType = 'today', trendingType = 'user', dateFrom: dateFromString, dateTo: dateToString } = data;
    if (dateFromString && dateToString && dateFromString === dateToString) {
      timeType = 'day';
    }
    /**
     * TODO: find top jobs that have
     * - highest completions
     * - highest participants
     *  for now: count and sort jobsimulationprogresses
     *  later: sync total participants to jobsimulations or another collection and get from there
     */
    const dateField = trendingType === 'user' ? 'createdAt' : 'completedAt';
    const result = await JobSimulationProgress.model.aggregate([
      {
        $match: {
          jobStatus: 'public',
          [dateField]: {
            $gte: dateFrom,
            $lt: dateTo
          }
        }
      },
      {
        $project: {
          time: { $dateToString: { format: ['today', 'yesterday', 'day'].includes(timeType) ? "%H" : "%Y-%m-%d", date: `$${dateField}` } },
          jobSimulationId: 1,
        }
      },
      {
        $group: {
          _id: { time: "$time", jobSimulationId: "$jobSimulationId" },
          count: { $sum: 1 }
        }
      },
      {
        $sort: { "_id.time": -1, count: -1 }
      },
      {
        $group: {
          _id: "$_id.time",
          items: {
            $push: {
              jobSimulationId: "$_id.jobSimulationId",
              value: "$count"
            }
          }
        }
      },
      {
        $project: {
          time: "$_id",
          items: { $slice: ["$items", limit] },
          _id: 0
        }
      }
    ]);


    result.sort((a, b) => a.time - b.time);
    const jobIds = result.map((group) => group.items.map((job) => job.jobSimulationId)).flat();
    const uniqueJobIds = [...new Set(jobIds)];
    const jobSimulations = await JobSimulation.model.find({ jobSimulationId: { $in: uniqueJobIds }, status: 'public' }, { _id: -1, jobSimulationId: 1, name: 1 }).lean();
    const defaultItems = jobSimulations.map(job => ({
      jobSimulationId: job.jobSimulationId,
      name: job.name,
      value: 0,
    }));

    result.forEach((group) => {
      const fulfilledItems = jobSimulations.map(job => {
        const value = group.items.find((item) => item.jobSimulationId === job.jobSimulationId)?.value || 0;
        return {
          jobSimulationId: job.jobSimulationId,
          name: job.name,
          value,
        };
      });
      fulfilledItems.sort((a, b) => b.name - a.name);
      group.items = fulfilledItems;
    });

    const labels = getTimeLabels(timeType, dateFrom, dateTo);
    const finalResult = labels.map((label) => {
      const group = result.find((group) => group.time === label);
      return {
        time: label,
        items: group ? group.items : defaultItems,
      };
    });

    // TODO: cache 1 minute
    return finalResult;
  },

  async getConversion(data) {
    if (!data.timeType) data.timeType = 'all';
    const { dateFrom, dateTo } = getDateRange(data);
    const filterDateSessions = dateFrom && dateTo ? {
      $or: [
        { createdAt: { $gte: dateFrom, $lt: dateTo } },
        { completedAt: { $gte: dateFrom, $lt: dateTo } },
      ]
    } : {};
    const totalPromise = JobSimulationProgress.model.countDocuments({
      jobStatus: 'public',
      ...(data.jobSimulationId ? { jobSimulationId: data.jobSimulationId } : {}),
      ...filterDateSessions,
    });
    const progressResultsPromise = JobSimulationProgress.model.aggregate([
      {
        $match: {
          jobStatus: 'public',
          status: 'completed',
          ...(data.jobSimulationId ? { jobSimulationId: data.jobSimulationId } : {}),
          ...(dateFrom && dateTo ? { completedAt: { $gte: dateFrom, $lt: dateTo } } : {}),
        }
      },
      {
        $facet: {
          counts: [
            {
              $group: {
                _id: null,
                totalCompletedJobs: { $sum: 1 },
                totalReferenceLetters: { $sum: { $cond: [{ $eq: ["$claimedCertification", true] }, 1, 0] } }
              }
            }
          ],
        }
      },
      {
        $project: {
          totalCompletedJobs: { $arrayElemAt: ["$counts.totalCompletedJobs", 0] },
          totalReferenceLetters: { $arrayElemAt: ["$counts.totalReferenceLetters", 0] },
        }
      }
    ]);

    const [total, progressResults] = await Promise.all([totalPromise, progressResultsPromise]);
    const totalCompletedJobs = progressResults?.[0].totalCompletedJobs || 0;
    const totalReferenceLetters = progressResults?.[0].totalReferenceLetters || 0;

    const completionRate = Math.round(((totalCompletedJobs / total) * 100) * 100) / 100;
    const referenceLetterRate = Math.round(((totalReferenceLetters / total) * 100) * 100) / 100;

    return [
      {
        name: 'Started',
        value: total,
        percentage: 100,
      },
      {
        name: 'Job Completed',
        value: totalCompletedJobs,
        percentage: completionRate,
      },
      {
        name: 'Letter Issued',
        value: totalReferenceLetters,
        percentage: referenceLetterRate,
      },
    ];
  },

  async getTopJobs(data) {
    let { limit = 5 } = data || {};
    limit = Number.parseInt(limit);
    if (limit < 3 || limit > 5) limit = 5;
    if (!data.timeType) data.timeType = 'last3months';
    const { dateFrom, dateTo } = getDateRange(data);
    // find top jobs that have - highest completions
    const result = await JobSimulationProgress.model.aggregate([
      {
        $match: {
          jobStatus: 'public',
          completedAt: {
            $gte: dateFrom,
            $lt: dateTo
          }
        }
      },
      {
        $group: {
          _id: "$jobSimulationId",
          count: { $sum: 1 }
        }
      },
      {
        $sort: { count: -1 }
      },
      {
        $limit: limit
      }
    ]);

    const jobSimulationIds = result.map((r) => r._id);
    const jobSimulations = await JobSimulation.model.find({ jobSimulationId: { $in: jobSimulationIds }, status: 'public' }, { _id: -1, jobSimulationId: 1, name: 1 }).lean();


    return jobSimulations;
  },

  async getOverviewTopProfiles(data) {
    /**
     * limit: 10,
     * timeType: 'today', 'yesterday', 'week', 'month', 'custom',
     * dateFrom, dateTo
     * jobSimulationId
     */
    let { limit = 10, maxCompletionTime = 360 } = data || {};
    limit = Number.parseInt(limit);
    if (limit < 5 || limit > 10) limit = 10;
    if (maxCompletionTime < 10 || maxCompletionTime > 600) maxCompletionTime = 360;

    if (!data.timeType) data.timeType = 'last3months';
    const { dateFrom, dateTo } = getDateRange(data);
    const filterDate = dateFrom && dateTo ? {
      completedAt: {
        $gte: dateFrom,
        $lt: dateTo
      }
    } : {};

    const result = await JobSimulationProgress.model.aggregate([
      {
        $match: {
          jobStatus: 'public',
          status: 'completed',
          ...filterDate,
          ...(data.jobSimulationId ? { jobSimulationId: data.jobSimulationId } : {})
        }
      },
      {
        $addFields: {
          // TODO: pre-calculate
          completionMins: {
            $divide: [
              { $subtract: ["$completedAt", "$createdAt"] },
              1000 * 60
            ]
          }
        }
      },
      {
        $match: {
          completionMins: { $lte: maxCompletionTime }
        }
      },
      {
        $sort: {
          scores: -1,
          completedAt: -1,
          completionMins: 1,
        }
      },
      {
        $limit: limit
      },
      {
        $project: {
          id: { $toString: "$_id" },
          _id: 0,
          email: 1,
          userId: 1,
          jobSimulationId: 1,
          scores: 1,
          skills: 1,
          completionMins: 1,
          note: 1,
        }
      }
    ]);

    if (!result?.length) return [];
    const uniqueJobIds = [...new Set(result.map((r) => r.jobSimulationId))];
    const uniqueUserId = [...new Set(result.map((r) => r.userId))];
    const [jobSimulations, users] = await Promise.all([
      JobSimulation.model.find({ jobSimulationId: { $in: uniqueJobIds } }, { _id: 0, jobSimulationId: 1, name: 1 }).lean(),
      User.find({ userId: { $in: uniqueUserId } }, { _id: 0, id: { $toString: "$_id" }, name: 1, avatar: 1, email: 1 }).lean(),
    ]);
    const jobSimulationsMap = jobSimulations.reduce((acc, curr) => {
      acc[curr.jobSimulationId] = curr;
      return acc;
    }, {});
    const usersMap = users.reduce((acc, curr) => {
      acc[curr.id] = curr;
      return acc;
    }, {});

    return result.map((r) => ({
      ...r,
      completionMins: Math.round(r.completionMins),
      jobSimulation: jobSimulationsMap[r.jobSimulationId],
      user: usersMap[r.userId],
    }));
  },

  async getJobOverview(data) {
    const { jobSimulationId } = data;

    if (!jobSimulationId) throw new Error("Job simulation ID is required");

    const jobSimulationPromise = JobSimulation.model.findOne({ jobSimulationId }, { _id: 0, name: 1, jobSimulationId: 1, description: 1, banner: 1 }).lean();
    const feedbackStatsPromise = jobSimulationFeedback.calculateAvgScoresByJobSimulation({ jobSimulationId });
    const progressResultsPromise = JobSimulationProgress.model.aggregate([
      {
        $match: {
          jobStatus: 'public',
          jobSimulationId,
        }
      },
      {
        $facet: {
          counts: [
            {
              $addFields: {
                completionMins: {
                  $divide: [
                    { $subtract: ["$completedAt", "$createdAt"] },
                    1000 * 60
                  ]
                }
              }
            },
            {
              $group: {
                _id: null,
                totalAttempts: { $sum: 1 },
                totalCompletedJobs: { $sum: { $cond: [{ $eq: ["$status", 'completed'] }, 1, 0] } },
                totalReferenceLetters: { $sum: { $cond: [{ $eq: ["$claimedCertification", true] }, 1, 0] } },
                avgCompletionMins: { $avg: "$completionMins" },
                minCompletionMins: { $min: "$completionMins" },
                maxCompletionMins: { $max: "$completionMins" }
              }
            }
          ],
          totalParticipants: [
            {
              $group: {
                _id: "$email",
              }
            },
            {
              $count: "totalParticipants"
            }
          ]
        }
      },
      {
        $project: {
          totalAttempts: { $arrayElemAt: ["$counts.totalAttempts", 0] },
          totalCompletedJobs: { $arrayElemAt: ["$counts.totalCompletedJobs", 0] },
          totalReferenceLetters: { $arrayElemAt: ["$counts.totalReferenceLetters", 0] },
          avgCompletionMins: { $arrayElemAt: ["$counts.avgCompletionMins", 0] },
          minCompletionMins: { $arrayElemAt: ["$counts.minCompletionMins", 0] },
          maxCompletionMins: { $arrayElemAt: ["$counts.maxCompletionMins", 0] },
          totalParticipants: { $ifNull: [{ $arrayElemAt: ["$totalParticipants.totalParticipants", 0] }, 0] }
        }
      }
    ]);

    const [jobSimulation, feedbackStats, progressResults] = await Promise.all([jobSimulationPromise, feedbackStatsPromise, progressResultsPromise]);

    return {
      jobSimulation,
      overview: {
        totalAttempts: progressResults?.[0].totalAttempts || 0,
        totalCompletedJobs: progressResults?.[0].totalCompletedJobs || 0,
        totalReferenceLetters: progressResults?.[0].totalReferenceLetters || 0,
        avgCompletionRate: Math.round(((progressResults?.[0].totalCompletedJobs || 0) / (progressResults?.[0].totalAttempts || 0)) * 100),
        totalParticipants: progressResults?.[0].totalParticipants || 0,
        avgCompletionMins: progressResults?.[0].avgCompletionMins || 0,
        minCompletionMins: progressResults?.[0].minCompletionMins || 0,
        maxCompletionMins: progressResults?.[0].maxCompletionMins || 0,
        avgFeedbackScores: feedbackStats?.avgScores || 0,
      },
    };
  },

  async getJobSimulationTopProfiles(data) {
    /**
     * limit: 10,
     * TODO: dateFrom, dateTo
     * jobSimulationId
     */
    let { limit = 10, maxCompletionTime = 1440, orderField = 'completionTime', searchCandidate = '', page = 1, jobSimulationId } = data || {};
    limit = Number.parseInt(limit);
    if (limit < 5 || limit > 50) limit = 5;
    if (maxCompletionTime < 10 || maxCompletionTime > 24 * 60) maxCompletionTime = 1440;

    const skip = (page - 1) * limit;

    let emails = null;
    if (!!searchCandidate?.length && searchCandidate.trim().length > 2) {
      const users = await User.find(
        {
          $or: [
            {
              name: { $regex: searchCandidate, $options: 'i' }
            },
            {
              email: { $regex: searchCandidate, $options: 'i' }
            }
          ]
        },
        { email: 1 }
      ).limit(50);
      emails = (users || []).map((u) => u.email);
    }

    const sortOptions = {};
    if (orderField === 'completionTime') {
      sortOptions['completionMins'] = 1;
    } else if (orderField === 'scores') {
      sortOptions['scores'] = -1;
    }
    sortOptions['completedAt'] = -1;

    const result = await JobSimulationProgress.model.aggregate([
      {
        $match: {
          jobStatus: 'public',
          status: 'completed',
          completionMins: { $lte: maxCompletionTime },
          ...(jobSimulationId ? { jobSimulationId } : {}),
          ...(emails ? { email: { $in: emails } } : {}),
        }
      },
      {
        $facet: {
          total: [
            {
              $count: "total"
            }
          ],
          data: [
            {
              $sort: sortOptions
            },
            // TODO: using last _id to improve the performance
            {
              $skip: skip,
            },
            {
              $limit: limit,
            },
            {
              $project: {
                id: { $toString: "$_id" },
                _id: 0,
                email: 1,
                userId: 1,
                jobSimulationId: 1,
                scores: 1,
                skills: 1,
                completionMins: 1,
                note: 1,
              }
            }
          ]
        }
      },
    ]);

    const total = result?.[0]?.total?.[0]?.total || 0;
    let profiles = result?.[0]?.data || [];

    if (total === 0) {
      return {
        data: [],
        meta: {
          limit,
          page,
          totalPages: 0,
          total,
        }
      }
    }

    const uniqueJobIds = [...new Set(profiles.map((r) => r.jobSimulationId))];
    const uniqueUserId = [...new Set(profiles.map((r) => r.userId))];
    const [jobSimulations, users] = await Promise.all([
      JobSimulation.model.find({ jobSimulationId: { $in: uniqueJobIds } }, { _id: 0, jobSimulationId: 1, name: 1 }).lean(),
      User.find({ userId: { $in: uniqueUserId } }, { _id: 0, id: { $toString: "$_id" }, name: 1, avatar: 1, email: 1 }).lean(),
    ]);
    const jobSimulationsMap = jobSimulations.reduce((acc, curr) => {
      acc[curr.jobSimulationId] = curr;
      return acc;
    }, {});
    const usersMap = users.reduce((acc, curr) => {
      acc[curr.id] = curr;
      return acc;
    }, {});

    profiles = profiles.map((r) => ({
      ...r,
      completionMins: Math.round(r.completionMins),
      jobSimulation: jobSimulationsMap[r.jobSimulationId],
      user: usersMap[r.userId],
    }));

    return {
      data: profiles,
      meta: {
        limit,
        page,
        totalPages: Math.ceil(total / limit),
        total,
      }
    }
  },

  async getEmployerJobSimulations(data) {
    let { limit = 10, keyword = '', page = 1 } = data || {};
    limit = Number.parseInt(limit);
    if (limit < 5 || limit > 50) limit = 5;
    const skip = (page - 1) * limit;

    /**
     * TODO: Temporary logic:
     *  query jobsimulationprogresses
     *  groupBy jobsimulationId
     *  sortBy participants
     *  skip
     *  limit
     *  TODO: need to query status (public / private / inactive)
     * 
     *  Qyery jobsimulations to get information
     */

    // await JobSimulation.getJobSimulations(Object.assign(params, { fields: [], statuses: ['public'] }));

    let jobSimulationIds = null;
    let queriedJobs = null;
    if (keyword) {
      queriedJobs = await JobSimulation.model.find(
        {
          name: { $regex: keyword, $options: 'i' },
          status: 'public',
        },
        { _id: 0, jobSimulationId: 1, name: 1, description: 1, skills: 1 }
      ).limit(100).lean();
    }
    if (queriedJobs?.length) {
      jobSimulationIds = queriedJobs.map((job) => job.jobSimulationId);
    }

    if (jobSimulationIds !== null && !jobSimulationIds.length) {
      return {
        data: [],
        meta: {
          limit,
          page,
          totalPages: 0,
          total: 0,
        }
      }
    }

    const result = await JobSimulationProgress.model.aggregate([
      {
        $match: {
          jobStatus: 'public',
          ...(jobSimulationIds ? { jobSimulationId: { $in: jobSimulationIds } } : {}),
        }
      },
      {
        $group: {
          _id: "$jobSimulationId",
          totalAttempts: { $sum: 1 },
          totalCompleted: {
            $sum: {
              $cond: [{ $eq: ["$status", 'completed'] }, 1, 0]
            }
          }
        }
      },
      {
        $facet: {
          total: [
            {
              $count: "total"
            }
          ],
          data: [
            {
              $sort: { totalByJob: -1 }
            },
            {
              $skip: skip,
            },
            {
              $limit: limit,
            },
            {
              $project: {
                jobSimulationId: "$_id",
                totalAttempts: 1,
                totalCompleted: 1,
              }
            }
          ]
        }
      }
    ]);

    if (!result?.[0]?.total?.[0]?.total) {
      return {
        data: [],
        meta: {
          limit,
          page,
          totalPages: 0,
          total: 0,
        }
      }
    }

    const resultTotal = result?.[0]?.total?.[0]?.total || 0;
    const resultData = result?.[0]?.data || [];
    const mapObjProgressByJobId = resultData.reduce((acc, curr) => {
      acc[curr.jobSimulationId] = curr;
      return acc;
    }, {});

    let responseData = [];
    if (!queriedJobs) {
      queriedJobs = await JobSimulation.model.find(
        {
          jobSimulationId: { $in: Object.keys(mapObjProgressByJobId) },
          status: 'public',
        },
        { _id: 0, jobSimulationId: 1, name: 1, description: 1, skills: 1 }
      ).lean();
    }
    responseData = queriedJobs.map((job) => ({
      ...job,
      totalAttempts: mapObjProgressByJobId[job.jobSimulationId]?.totalAttempts || 0,
      totalCompleted: mapObjProgressByJobId[job.jobSimulationId]?.totalCompleted || 0,
    }));

    return {
      data: responseData,
      meta: {
        limit,
        page,
        totalPages: Math.ceil(resultTotal / limit),
        total: resultTotal,
      }
    }
  },
};

module.exports = JobSimulationStatisticService;
