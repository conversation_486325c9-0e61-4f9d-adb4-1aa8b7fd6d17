import { useEffect, useState } from 'react';
import AppModal from './AppModal';
import { useGetJobSimulationApps } from '~/data-provider/JobSimulation';
import { Skeleton, Popover, PopoverContent, PopoverTrigger } from '~/components/ui';
import { useAuthContext } from '~/hooks';
import {
  useCreateJobSimulationApp,
  useDeleleJobSimulationApp,
  useUpdateJobSimulationApp,
} from '~/data-provider/JobSimulation/mutations';
import { useToastContext } from '~/Providers';
import { EditIcon, DeleteIcon, PlusIcon } from '~/components/svg';
import type { TUser } from 'librechat-data-provider';
import type t from 'librechat-data-provider';

interface AppCreationProps {
  setAppSelected: (app: t.TJobSimulationApp) => void;
  appSelected: t.TJobSimulationApp | null;
  user: TUser | undefined;
}

export default function AppCreation({ setAppSelected, appSelected, user }: AppCreationProps) {
  const [openModal, setOpenModal] = useState(false);
  const [type, setType] = useState<'create' | 'edit'>('create');
  const [openDeletePopover, setOpenDeletePopover] = useState<string | null>(null);

  const { isAuthenticated } = useAuthContext();
  const { showToast } = useToastContext();

  const {
    data: apps,
    isLoading,
    refetch,
  } = useGetJobSimulationApps(
    {},
    {
      enabled: isAuthenticated,
      onSuccess: (data) => {
        // if (data && data.length > 0 && type !== 'edit') {
        //   setAppSelected(data[0]);
        // }
      },
    },
  );

  const createApp = useCreateJobSimulationApp({
    onSuccess: (data) => {
      setAppSelected(data);
      showToast({
        message: 'App created successfully',
        status: 'success',
      });
      setOpenModal(false);
      refetch();
      setAppSelected(data);
    },
    onError: (error) => {
      showToast({
        message: 'Failed to create app',
        status: 'error',
      });
    },
  });

  const updateApp = useUpdateJobSimulationApp({
    onSuccess: (data) => {
      showToast({
        message: 'App updated successfully',
        status: 'success',
      });
      setOpenModal(false);
      refetch();
      setAppSelected(data);
    },
    onError: (error) => {
      showToast({
        message: 'Failed to update app',
        status: 'error',
      });
    },
  });

  const deleleApp = useDeleleJobSimulationApp({
    onSuccess: () => {
      showToast({
        message: 'App deleted successfully',
        status: 'success',
      });
      refetch();
      setOpenDeletePopover(null);
    },
    onError: (error) => {
      showToast({
        message: 'Failed to delete app',
        status: 'error',
      });
    },
  });

  useEffect(() => {
    if (apps && apps.length > 0) {
      if (appSelected && type !== 'create') {
        const foundApp = apps.find((app) => app._id === appSelected._id);
        if (foundApp) {
          setAppSelected(foundApp);
        } else {
          setAppSelected(apps[0]);
        }
      } else if (!appSelected) {
        setAppSelected(apps[0]);
      }
    }
  }, [apps, appSelected, setAppSelected]);

  const handleCreateNewApp = () => {
    setType('create');
    setOpenModal(true);
  };

  const handleEditApp = (e, app) => {
    e.stopPropagation();
    setAppSelected(app);
    setType('edit');
    setOpenModal(true);
  };

  const handleRemoveApp = (e, app) => {
    setAppSelected(app);
    e.stopPropagation();
    deleleApp.mutate({ id: app._id, userId: user?.id });
  };

  const handleOk = (type, data) => {
    if (type === 'edit') {
      updateApp.mutate({ id: appSelected?._id, userId: user?.id, ...data });
    } else {
      createApp.mutate({ ...data, userId: user?.id, type: 'public' });
    }
  };

  return (
    <div className="grid grid-cols-2 gap-4 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6">
      <div
        className="group flex h-32 cursor-pointer flex-col items-center justify-center overflow-hidden rounded-xl border-2 border-dashed border-gray-300 bg-gray-50 transition-all duration-200 hover:cursor-pointer hover:border-purple-500 hover:bg-purple-50"
        onClick={handleCreateNewApp}
      >
        <div className="mb-2 rounded-full bg-purple-100 p-3 text-purple-500 transition-all duration-200 group-hover:bg-purple-200">
          <PlusIcon className="h-6 w-6" />
        </div>
        <div className="font-medium text-gray-700 group-hover:text-purple-600">Create new App</div>
      </div>

      {isLoading &&
        [...Array(5)].map((_, index) => (
          <div key={index} className="h-32 overflow-hidden rounded-xl">
            <Skeleton className="h-full w-full rounded-xl" />
          </div>
        ))}

      {!isLoading &&
        apps?.map((app, index) => (
          <div
            key={index}
            className={`group relative flex h-32 transform cursor-pointer flex-col rounded-xl border-2 p-3 duration-200 hover:scale-105 ${
              app._id === appSelected?._id
                ? 'border-purple-500 shadow-md shadow-purple-100'
                : 'border-gray-200 hover:border-purple-300'
            } `}
            onClick={() => setAppSelected(app)}
          >
            <div className="absolute right-2 top-2 flex gap-1 opacity-0 transition-opacity group-hover:opacity-100">
              <button
                className="flex h-7 w-7 items-center justify-center rounded-full bg-gray-800/80 p-1 text-white backdrop-blur-sm hover:bg-gray-700"
                onClick={(e) => handleEditApp(e, app)}
                title="Edit app"
              >
                <EditIcon />
              </button>

              <Popover
                open={openDeletePopover === app._id}
                onOpenChange={(open) => {
                  setOpenDeletePopover(open ? (app._id as string) : null);
                }}
              >
                <PopoverTrigger asChild>
                  <button
                    className="flex h-7 w-7 items-center justify-center rounded-full bg-gray-800/80 p-1 text-white backdrop-blur-sm hover:bg-red-600"
                    title="Remove app"
                    onClick={(e) => {
                      e.stopPropagation();
                      setAppSelected(app);
                    }}
                  >
                    <DeleteIcon />
                  </button>
                </PopoverTrigger>
                <PopoverContent
                  className="z-50 w-fit rounded-lg border border-gray-200 bg-white p-3 shadow-lg"
                  align="center"
                >
                  <div className="w-fit font-medium">Delete this app?</div>
                  <div className="mb-3 mt-1 text-xs text-gray-500">
                    This action cannot be undone.
                  </div>
                  <div className="flex justify-end">
                    <button
                      className="mr-2 rounded bg-gray-200 px-3 py-1 text-sm text-gray-700 hover:bg-gray-300"
                      onClick={(e) => {
                        e.stopPropagation();
                        setOpenDeletePopover(null);
                      }}
                    >
                      Cancel
                    </button>
                    <button
                      className="rounded bg-red-600 px-3 py-1 text-sm text-white hover:bg-red-700"
                      onClick={(e) => handleRemoveApp(e, app)}
                      disabled={deleleApp.isLoading}
                    >
                      {deleleApp.isLoading ? 'Deleting...' : 'Delete'}
                    </button>
                  </div>
                </PopoverContent>
              </Popover>
            </div>

            <div className="flex flex-grow items-center justify-center py-1">
              <img
                src={app.icon}
                alt={app.name}
                className="h-14 w-14 rounded-xl object-cover shadow-sm"
                onError={(e) => {
                  // Fallback if image fails to load
                  e.currentTarget.src = 'https://via.placeholder.com/56?text=App';
                }}
              />
            </div>

            <div className="mt-2 w-[150px]">
              <p
                className="line-clamp-1 overflow-hidden text-center text-sm font-medium text-black dark:text-white"
                title={app.name}
              >
                {app.name}
              </p>
            </div>
          </div>
        ))}

      {openModal && (
        <AppModal
          open={openModal}
          onClose={() => setOpenModal(false)}
          app={appSelected}
          type={type}
          onOk={handleOk}
          disabled={!isAuthenticated || !user || createApp.isLoading || updateApp.isLoading}
        />
      )}
    </div>
  );
}
