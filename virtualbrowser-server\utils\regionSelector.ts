const DEFAULT_REGION: BrowserbaseRegion = "ap-southeast-1";

type BrowserbaseRegion =
    | "us-west-2"
    | "us-east-1"
    | "eu-central-1"
    | "ap-southeast-1";

// Exact timezone matches for east coast cities
const exactTimezoneMap: Record<string, BrowserbaseRegion> = {
    "America/New_York": "us-east-1",
    "America/Detroit": "us-east-1",
    "America/Toronto": "us-east-1",
    "America/Montreal": "us-east-1",
    "America/Boston": "us-east-1",
    "America/Chicago": "us-east-1",
};

// Prefix-based region mapping
const prefixToRegion: Record<string, BrowserbaseRegion> = {
    America: "us-west-2",
    US: "us-west-2",
    Canada: "us-west-2",
    Europe: "eu-central-1",
    Africa: "eu-central-1",
    Asia: "ap-southeast-1",
    Australia: "ap-southeast-1",
    Pacific: "ap-southeast-1",
};

// Offset ranges to regions (inclusive bounds)
const offsetRanges: {
    min: number;
    max: number;
    region: BrowserbaseRegion;
}[] = [
        { min: -24, max: -4, region: "us-west-2" }, // UTC-24 to UTC-4
        { min: -3, max: 4, region: "eu-central-1" }, // UTC-3 to UTC+4
        { min: 5, max: 24, region: "ap-southeast-1" }, // UTC+5 to UTC+24
    ];

const getClosestRegion = (timezone?: string): BrowserbaseRegion => {
    try {
        if (!timezone) {
            return DEFAULT_REGION;
        }

        // Check exact matches first
        if (timezone in exactTimezoneMap) {
            return exactTimezoneMap[timezone];
        }

        // Check prefix matches
        const prefix = timezone.split("/")[0];
        if (prefix in prefixToRegion) {
            return prefixToRegion[prefix];
        }

        // Fallback server timezone
        const date = new Date();
        const formatter = new Intl.DateTimeFormat("en-US", { timeZone: timezone });
        const timeString = formatter.format(date);
        const testDate = new Date(timeString);
        const hourOffset = (testDate.getTime() - date.getTime()) / (1000 * 60 * 60);

        const matchingRange = offsetRanges.find(
            (range) => hourOffset >= range.min && hourOffset <= range.max
        );

        return matchingRange?.region ?? DEFAULT_REGION;
    } catch {
        return DEFAULT_REGION;
    }
}

export { getClosestRegion, BrowserbaseRegion };
