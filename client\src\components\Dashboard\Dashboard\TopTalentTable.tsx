import { dataService } from 'librechat-data-provider';
import { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { But<PERSON>, Tag, TooltipAnchor } from '~/components/ui';
import { cn } from '~/utils';
// import {
//   Radar,
//   RadarChart,
//   PolarGrid,
//   PolarAngleAxis,
//   PolarRadiusAxis,
//   ResponsiveContainer,
// } from 'recharts';

const medals = ['🥇', '🥈', '🥉'];
const skillLabels = {
  1: 'Novice',
  2: 'Basic',
  3: 'Good',
  4: 'Very Good',
  5: 'Excellent',
};
const minutesToHours = (minutes: number) => {
  const hours = Math.floor(minutes / 60);
  const mins = minutes % 60;
  return `${hours}h ${mins}m`;
};

const buildProfiles = (data: any[]) => {
  return data.map((item, index) => ({
    rank: index + 1,
    name: item.user?.name,
    email: item.user?.email,
    id: item.user?.id,
    scores: item.scores,
    skills: item.skills,
    note: item.note,
    jobSimulationName: item.jobSimulation?.name,
    completionTime: minutesToHours(item.completionMins || 0),
    medal: medals[index] || (index + 1).toString(),
  }));
};

const getTopProfilesAsync = async (params: any): Promise<any> => {
  const result = await dataService.getEmployerDashboardOverviewTopProfiles(params);
  return buildProfiles(result?.data || []);
};

const getTopJobsAsync = async (params: any): Promise<any> => {
  const result = await dataService.getEmployerDashboardTopJobs(params);
  //   const result = {
  //     status: 'success',
  //     data: [
  //       {
  //         _id: '68193c186eae2c750ea99df4',
  //         name: 'Digital Marketing Analyst',
  //         jobSimulationId: 'digital-marketing',
  //       },
  //       {
  //         _id: '6818de396eae2c750ea99df3',
  //         name: 'ESG Analyst',
  //         jobSimulationId: 'esg-analyst',
  //       },
  //       {
  //         _id: '682407d5b5f1096fe4a7c35f',
  //         name: 'Marketing',
  //         jobSimulationId: 'lucas-test',
  //       },
  //     ],
  //   };
  return result?.data || [];
};

// const SkillRadarChart = ({
//   name,
//   skills,
// }: {
//   name: string;
//   skills: { name: string; rating: number }[];
// }) => {
//   return (
//     <ResponsiveContainer width="100%" height="100%">
//       <RadarChart cx="50%" cy="50%" outerRadius="80%" data={skills}>
//         <PolarGrid />
//         <PolarAngleAxis dataKey="name" fontSize={10} fontSizeAdjust={0.5} />
//         <PolarRadiusAxis />
//         <Radar name={name} dataKey="rating" stroke="#8884d8" fill="#8884d8" fillOpacity={0.6} />
//       </RadarChart>
//     </ResponsiveContainer>
//   );
// };

const TopTalentTable = () => {
  const navigate = useNavigate();
  const [jobSimulationId, setJobSimulationId] = useState<string>('alljobs');
  const [jobSimulationName, setJobSimulationName] = useState<string>('');
  const [jobSimulations, setJobSimulations] = useState<{ jobSimulationId: string; name: string }[]>(
    [],
  );
  const [profiles, setProfiles] = useState<
    {
      rank: number;
      name: string;
      email: string;
      id: string;
      jobSimulationName: string;
      completionTime: string;
      scores: number;
      note?: string;
      skills?: { name: string; rating: number }[];
      medal: string;
    }[]
  >([]);
  const [isGettingData, setIsGettingData] = useState(false);

  useEffect(() => {
    getTopJobsAsync({ limit: 5 }).then((data) => {
      setJobSimulations([{ jobSimulationId: 'alljobs', name: 'All' }, ...data]);
    });
  }, []);

  useEffect(() => {
    if (isGettingData) return;
    setIsGettingData(true);
    getTopProfilesAsync({
      jobSimulationId: jobSimulationId === 'alljobs' ? undefined : jobSimulationId,
      limit: 10,
    })
      .then((data) => {
        setProfiles(data);
      })
      .finally(() => {
        setIsGettingData(false);
      });
  }, [jobSimulationId]);

  return (
    <div className="rounded-xl bg-white p-6 text-[#505050] drop-shadow-lg">
      <div className="mb-4 flex items-center justify-between">
        <h3 className="text-xl font-semibold">Top Talent Profiles</h3>
      </div>
      <hr />
      <div className="my-4 flex flex-wrap">
        {jobSimulations.map((jobSimulation) => (
          <Button
            key={jobSimulation.jobSimulationId}
            size="sm"
            onClick={() => {
              if (isGettingData) return;
              setJobSimulationId(jobSimulation.jobSimulationId);
              setJobSimulationName(jobSimulation.name);
            }}
            disabled={isGettingData}
            className={cn(
              jobSimulationId === jobSimulation.jobSimulationId
                ? 'bg-blue-100 font-light text-blue-600 hover:bg-blue-100'
                : 'bg-transparent font-light text-gray-500 hover:bg-transparent',
            )}
          >
            {jobSimulation.name}
          </Button>
        ))}
      </div>
      <div className="mt-4 w-full flex-1 overflow-x-auto">
        <table className="w-full">
          <thead className="border-b border-gray-200">
            <tr className="text-left">
              <th className="pb-3 text-sm font-light text-gray-500 opacity-60">Top</th>
              <th className="pb-3 text-sm font-light text-gray-500 opacity-60">Candidate Name</th>
              <th className="pb-3 text-sm font-light text-gray-500 opacity-60">Job Simulation</th>
              <th className="pb-3 text-sm font-light text-gray-500 opacity-60">Skills</th>
              <th className="pb-3 text-sm font-light text-gray-500 opacity-60">Note</th>
              <th className="pb-3 text-sm font-light text-gray-500 opacity-60">Score</th>
              <th className="pb-3 text-sm font-light text-gray-500 opacity-60">Completion Time</th>
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-100">
            {profiles.map((profile) => (
              <tr key={profile.rank} className="hover:bg-gray-50">
                <td className="max-w-16 py-4">
                  <span className="font-medium text-gray-500">{profile.medal}</span>
                </td>
                <td className="py-4">
                  <div>
                    <div className="text-sm font-medium text-gray-600">{profile.name}</div>
                    <div className="text-sm text-gray-500">{profile.email}</div>
                  </div>
                </td>
                <td className="max-w-36 py-4">
                  <div className="text-sm font-medium text-gray-600">
                    {profile.jobSimulationName}
                  </div>
                </td>
                <td className="max-w-56 py-4">
                  <div className="flex flex-wrap gap-2 text-sm font-medium text-gray-600">
                    {/* May be we could apply the radar chart for skills */}
                    {(profile.skills || [])
                      .sort((s1, s2) => s2.rating - s1.rating)
                      .slice(0, 2)
                      .map((skill) => (
                        <Tag
                          key={skill.name}
                          label={skill.name}
                          labelClassName="w-auto px-2 ml-0 truncate"
                          title={skillLabels[skill.rating] || 'Good'}
                        />
                      ))}
                    {(profile.skills || []).length > 2 && (
                      <TooltipAnchor
                        descriptionComponent={
                          <div className="flex flex-wrap gap-2 text-sm font-medium text-gray-600">
                            {(profile.skills || []).map((skill) => (
                              <div key={skill.name}>
                                <Tag
                                  label={skill.name}
                                  labelClassName="w-auto px-2 ml-0"
                                  title={skillLabels[skill.rating] || 'Good'}
                                />
                              </div>
                            ))}
                          </div>
                        }
                        side="right"
                        toolTipClassName="max-w-[300px]"
                        render={<Tag label="..." labelClassName="w-auto px-2 ml-0" />}
                      />
                    )}
                  </div>
                </td>
                <td className="py-4">
                  <div className="text-sm font-medium text-gray-600">{profile.note}</div>
                </td>
                <td className="max-w-16 py-4">
                  <div className="text-sm font-medium text-gray-600">{profile.scores || '-'}</div>
                </td>
                <td className="py-4">
                  <div className="text-sm font-medium text-gray-600">{profile.completionTime}</div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
        <hr />
      </div>

      <div className="mt-6 flex justify-center">
        <Button
          variant="link"
          className="font-light italic text-gray-600 underline"
          onClick={() => {
            navigate(
              `/job-simulation/dashboard/candidates${jobSimulationId && jobSimulationId !== 'alljobs' ? `?jobSimulationId=${jobSimulationId}&jobSimulationName=${jobSimulationName}` : ''}`,
            );
          }}
        >
          View detail
        </Button>
      </div>
    </div>
  );
};

export default TopTalentTable;
