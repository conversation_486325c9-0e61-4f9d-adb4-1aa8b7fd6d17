import { useEffect, useRef, useState } from 'react';
import { useSetRecoilState } from 'recoil';
import {
  AppSimulationElementDropdownOption,
  AppSimulationModalConfig,
  AppSimulationScreen,
  AppSimulationScreenPlaceholder,
} from '~/common';
import { Tag } from '~/components';
import {
  createCalculationEngine,
  findScreenIndexById,
  formatValueWithType,
  getElementStyles,
  getPlaceholderStyle,
  getRandomBetween,
  getResponsiveFontSize,
  hasTimeBasedPlaceholders,
  safeCalculateEstimates,
  uploadAppSimulationPhoto,
} from '~/components/JobSimulation/AppSimulation/AppSimulationHelpers';
import { CalculationConfig } from '~/components/JobSimulation/AppSimulation/CalculationEngine';
import DialogTextInput from '~/components/JobSimulation/AppSimulation/DialogTextInput';
import store from '~/store';
import { cn } from '~/utils';
import AppSimulationDropdown from './AppSimulationDropdown';
import DialogDynamicInputs from './DialogDynamicInputs';
import LineChartAutoTime from './LineChartAutoTime';
import LineChartDynamic from './LineChartDynamic';
import PlaceholderImage from './Placeholder/PlaceholderImage';
import MetricsBarChart from './BarChart';

interface AppSimulationProps {
  appSimulationScreens: AppSimulationScreen[];
  appSimulationConfig?: CalculationConfig;
}

const AppSimulation = ({ appSimulationScreens, appSimulationConfig }: AppSimulationProps) => {
  console.log('::: AppSimulation :::');
  const setJobsimulationTriggerMessage = useSetRecoilState(store.jobSimulationTriggerMessage);

  const imageRef: React.MutableRefObject<HTMLImageElement | null> = useRef(null);
  const containerRef: React.MutableRefObject<HTMLDivElement | null> = useRef(null);
  const imageContainerRef = useRef<HTMLDivElement | null>(null);

  const [currentScreenIndex, setCurrentScreenIndex] = useState(0);
  const [previousSreenIndex, setPreviousScreenIndex] = useState(0);
  const [showDropdown, setShowDropdown] = useState(false);
  const [dropdownOptions, setDropdownOptions] = useState<Array<AppSimulationElementDropdownOption>>(
    [],
  );
  const [dropdownPosition, setDropdownPosition] = useState({ x: 0, y: 0 });
  // Use in message
  const [userSelections, setUserSelections] = useState<Record<string, string>>({});

  // State for data content. Use for displaying in placeholders
  const [dataContent, setDataContent] = useState<
    Record<
      string,
      {
        originalValue?: any;
        value?: any;
        valueType?: 'number' | 'string' | 'imgPath';
      }
    >
  >({});

  const [hoveredElements, setHoveredElements] = useState<number[]>([]);

  // State for text input dialog
  const [showTextDialog, setShowTextDialog] = useState(false);
  const [textInputData, setTextInputData] = useState<{
    type: string;
    value: string;
    dataContextId: string;
    saveToSelections: boolean;
    dataContextLabel: string;
  }>({
    type: 'input',
    value: '',
    dataContextId: '',
    saveToSelections: false,
    dataContextLabel: '',
  });

  // State for dynamic modal
  const [showModal, setShowModal] = useState(false);
  const [currentModalConfig, setCurrentModalConfig] = useState<AppSimulationModalConfig | null>(
    null,
  );
  const [modalValues, setModalValues] = useState<Record<string, any>>({});

  // State for time-based placeholders
  // const [elapsedTime, setElapsedTime] = useState(0);
  const [startTime, setStartTime] = useState<number | null>(null);
  const [timeBasedValues, setTimeBasedValues] = useState<Record<string, string>>({});
  const [lastUpdateTimes, setLastUpdateTimes] = useState<Record<string, number>>({});

  // Initialize Calculation Engine dynamically
  const [calculationEngine] = useState(() => {
    return createCalculationEngine(appSimulationConfig);
  });

  // Getefault values for calculation config
  const [dynamicSelections, setDynamicSelections] = useState<Record<string, any>>(() => {
    return calculationEngine?.getDefaultValues() || {};
  });
  const [calculatedEstimates, setCalculatedEstimates] = useState<Record<string, string>>({});

  // State for responsive font scaling
  const [containerWidth, setContainerWidth] = useState<number>(1200);
  const resizeObserverRef = useRef<ResizeObserver | null>(null);

  // Calculate parameters change using Calculation Engine
  useEffect(() => {
    // Only skip if dynamicSelections is null/undefined, not if it's empty object
    if (!dynamicSelections || !calculationEngine || !calculationEngine.getDefaultValues()) return;

    const estimates = safeCalculateEstimates(calculationEngine, dynamicSelections);
    setCalculatedEstimates(estimates);
  }, [dynamicSelections, calculationEngine]);

  useEffect(() => {
    setShowDropdown(false);

    if (appSimulationScreens[currentScreenIndex].actions?.length) {
      for (const action of appSimulationScreens[currentScreenIndex].actions) {
        if (action.type === 'triggerMessage') {
          let finalMessage = action.message || '';

          if (action.withData && Object.keys(userSelections).length > 0) {
            const selectionsText = Object.entries(userSelections)
              .map(([key, value]) => `${key}: ${value}`)
              .join(', ');
            finalMessage = `${finalMessage} ${selectionsText}`;
            console.log('::: finalMessage ::: ', finalMessage);
            setJobsimulationTriggerMessage({
              message: finalMessage,
              isTriggered: true,
            });
          }

          break;
        }
      }
    }

    // Init timer for screens have time-based placeholders
    if (hasTimeBasedPlaceholders(appSimulationScreens[currentScreenIndex])) {
      setStartTime(Date.now());
      // setElapsedTime(0);
      setLastUpdateTimes({});
      // Initialize time-based values
      const initialValues: Record<string, string> = {};
      appSimulationScreens[currentScreenIndex].placeholders?.forEach((placeholder) => {
        if (placeholder.dataByTime) {
          // Find initial value (time = 0)
          const initialData = placeholder.dataByTime.find((data) => data.time === 0);
          if (placeholder.dataId && dataContent[placeholder.dataId]?.value) {
            initialValues[placeholder.id] = dataContent[placeholder.dataId]?.value!;
          } else if (initialData) {
            initialValues[placeholder.id] = initialData.value;
          }
        } else if (placeholder.increaseByTime && placeholder.increaseSettings?.from !== undefined) {
          if (placeholder.dataId && dataContent[placeholder.dataId]?.value) {
            initialValues[placeholder.id] = dataContent[placeholder.dataId]?.value!;
          } else {
            initialValues[placeholder.id] = placeholder.increaseSettings.from.toString();
          }
        }
      });
      setTimeBasedValues(initialValues);
    }
  }, [currentScreenIndex]);

  // Timer for time-based placeholders
  useEffect(() => {
    if (startTime === null || !hasTimeBasedPlaceholders(appSimulationScreens[currentScreenIndex]))
      return;

    const interval = setInterval(() => {
      const currentTime = Date.now();
      const elapsed = Math.floor((currentTime - startTime) / 1000); // Convert to seconds
      // setElapsedTime(elapsed);

      // Update time-based values
      const updatedValues: Record<string, string> = {};
      appSimulationScreens[currentScreenIndex].placeholders?.forEach((placeholder) => {
        if (placeholder.dataByTime) {
          // Find the appropriate value for current elapsed time
          let currentValue = '0';
          for (let i = placeholder.dataByTime.length - 1; i >= 0; i--) {
            if (elapsed >= placeholder.dataByTime[i].time) {
              currentValue = placeholder.dataByTime[i].value;
              break;
            }
          }
          updatedValues[placeholder.id] = currentValue;
        } else if (placeholder.increaseByTime && placeholder.increaseSettings?.from !== undefined) {
          // Update every 10 seconds with random increase
          let currentVal = Number(
            timeBasedValues[placeholder.id] || placeholder.increaseSettings.from.toString(),
          );
          currentVal = Number.isNaN(currentVal) ? 0 : currentVal;
          const lastUpdate = lastUpdateTimes[placeholder.id] || 0;
          const interval = placeholder.increaseSettings?.interval || 10;
          const currentInterval = Math.floor(elapsed / interval);
          const lastInterval = Math.floor(lastUpdate / interval);

          if (elapsed >= interval && currentInterval > lastInterval) {
            // Time for an update - add random increase
            const increaseRange = placeholder.increaseSettings?.randomRange || [1, 10];
            const randomIncrease = getRandomBetween(increaseRange);

            const newValue = currentVal + randomIncrease;
            updatedValues[placeholder.id] = newValue.toString();
            if (placeholder.dataId) {
              setDataContent((prev) => ({
                ...prev,
                [placeholder.dataId!]: {
                  ...prev[placeholder.dataId!],
                  value: newValue.toString(),
                },
              }));
            }
            console.log(
              `Updating ${placeholder.id}: ${currentVal} + ${randomIncrease} = ${newValue} at ${elapsed}s`,
            );
            // Update last update time
            setLastUpdateTimes((prev) => ({
              ...prev,
              [placeholder.id]: elapsed,
            }));
          } else {
            // Keep current value
            updatedValues[placeholder.id] = currentVal.toString();
            if (placeholder.dataId) {
              setDataContent((prev) => ({
                ...prev,
                [placeholder.dataId!]: {
                  ...prev[placeholder.dataId!],
                  value: currentVal.toString(),
                },
              }));
            }
          }
        }
      });

      setTimeBasedValues((prev) => ({ ...prev, ...updatedValues }));
    }, 1000); // Update every second

    return () => clearInterval(interval);
  }, [startTime, currentScreenIndex, timeBasedValues]);

  // Handle work portal resize --> image resize and calculate scale factor
  useEffect(() => {
    function calculateImageScale() {
      if (imageRef.current && containerRef.current) {
        const containerHeight = containerRef.current.clientHeight ?? 0;

        imageRef.current.style.width = `auto`;
        imageRef.current.style.maxHeight = `${containerHeight}px`;

        if (imageContainerRef.current) {
          imageContainerRef.current.style.width = `auto`;
          imageContainerRef.current.style.maxHeight = `${containerHeight}px`;
        }

        setTimeout(() => {
          if (imageRef.current && containerRef.current) {
            const {
              naturalWidth,
              naturalHeight,
              clientWidth: actualWidth,
              clientHeight: actualHeight,
            } = imageRef.current;

            // Only calculate if image is properly loaded
            if (naturalWidth > 0 && naturalHeight > 0 && actualWidth > 0 && actualHeight > 0) {
              setContainerWidth(containerRef.current.clientWidth);
            }
          }
        }, 50);
      }
    }

    // Setup ResizeObserver for better performance
    if (containerRef.current && !resizeObserverRef.current) {
      resizeObserverRef.current = new ResizeObserver(() => {
        // Use requestAnimationFrame to avoid layout thrashing
        requestAnimationFrame(calculateImageScale);
      });
      resizeObserverRef.current.observe(containerRef.current);
    }

    // Initial calculation when screen changes
    calculateImageScale();

    // Cleanup function
    return () => {
      if (resizeObserverRef.current) {
        resizeObserverRef.current.disconnect();
        resizeObserverRef.current = null;
      }
    };
  }, [imageRef.current?.src, currentScreenIndex]); // Removed problematic dependencies

  // Cleanup ResizeObserver
  useEffect(() => {
    return () => {
      if (resizeObserverRef.current) {
        resizeObserverRef.current.disconnect();
        resizeObserverRef.current = null;
      }
    };
  }, []);

  useEffect(() => {
    if (imageRef.current) {
      imageRef.current.src = appSimulationScreens[currentScreenIndex].image;
    }
  }, [imageRef, currentScreenIndex]);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = () => {
      if (showDropdown) {
        setShowDropdown(false);
      }
    };

    if (showDropdown) {
      document.addEventListener('click', handleClickOutside);
    }

    return () => {
      document.removeEventListener('click', handleClickOutside);
    };
  }, [showDropdown]);

  // Handle removing tag from placeholder
  const handleRemoveTag = (placeholderId: string, dataId: string, tagToRemove: string) => {
    const currentContent = dataContent[dataId];
    if (!currentContent?.value) return;

    const separator =
      appSimulationScreens[currentScreenIndex]?.placeholders?.find((p) => p.id === placeholderId)
        ?.dataSeparator || ', ';

    const currentTags = currentContent.value.split(separator).map((tag: string) => tag.trim());
    const updatedTags = currentTags.filter((tag: string) => tag !== tagToRemove);
    const updatedValue = updatedTags.join(separator);

    // Update dataContent
    setDataContent((prev) => ({
      ...prev,
      [dataId]: {
        ...prev[dataId],
        value: updatedValue,
      },
    }));

    // Update userSelections if needed
    setUserSelections((prev) => {
      const updatedSelections = { ...prev };
      Object.keys(updatedSelections).forEach((key) => {
        if (key === dataId) {
          updatedSelections[key] = `Languages: ${updatedValue}`;
        }
      });
      return updatedSelections;
    });

    // Update dynamic selections for calculation
    setDynamicSelections((prev) => ({
      ...prev,
      [dataId]: updatedTags,
    }));
  };

  // Render placeholder content based on display type
  const renderPlaceholderContent = (
    placeholder: AppSimulationScreenPlaceholder,
    displayText: string,
  ) => {
    if (placeholder.dataDisplayType === 'tag' && displayText) {
      const separator = placeholder.dataSeparator || ', ';
      const tags = displayText
        .split(separator)
        .map((tag) => tag.trim())
        .filter(Boolean);

      // Calculate responsive font size for tags using container width
      const responsiveConfig = placeholder.responsiveConfig || {};
      const baseFontSize = responsiveConfig.baseFontSize || 0.75;
      const minFontSize = responsiveConfig.minFontSize || 0.3;
      const maxFontSize = responsiveConfig.maxFontSize || 1.2;
      const scaleWithImage = responsiveConfig.scaleWithImage !== false; // Default true

      let calculatedFontSize = baseFontSize;
      if (scaleWithImage) {
        calculatedFontSize = getResponsiveFontSize(
          containerWidth,
          baseFontSize,
          minFontSize,
          maxFontSize,
        );
      }

      return (
        <div className="flex flex-wrap gap-1">
          {tags.map((tag, index) => (
            <Tag
              key={`${placeholder.id}-tag-${index}`}
              label={tag}
              onRemove={() => handleRemoveTag(placeholder.id, placeholder.dataId!, tag)}
              className={cn('font-medium', containerWidth < 1200 && 'max-h-3')}
              cancelButtonClassName={cn(containerWidth < 1200 && 'max-h-3 max-w-3')}
              labelClassName="p-1"
              style={{
                fontSize: `${calculatedFontSize}rem`,
              }}
            />
          ))}
        </div>
      );
    }

    // Default text display
    return displayText || '';
  };

  // Handle photo upload
  const handlePhotoUpload = (contextId: string) => {
    const callbackImage = (imgUrl: any) => {
      // Save image data by dataId instead of screenId
      setDataContent((prev) => ({
        ...prev,
        [contextId]: {
          ...prev[contextId],
          value: imgUrl,
        },
      }));
    };
    uploadAppSimulationPhoto({ callbackImage, width: 480, height: 480 });
  };

  // Handle text input
  const handleTextInput = (
    inputType: 'input' | 'textarea' = 'input',
    label?: string,
    dataContextId?: string,
    saveToSelections?: boolean,
  ) => {
    // Get current text value from dataContent using dataContextId
    setTextInputData({
      type: inputType,
      value: dataContent[dataContextId || 'unknown']?.value || '',
      dataContextId: dataContextId || '',
      saveToSelections: saveToSelections || false,
      dataContextLabel: label || 'Text Content',
    });
    setShowTextDialog(true);
  };

  // Handle text dialog submit
  const handleTextDialogSubmit = () => {
    if (textInputData.dataContextId) {
      setDataContent((prev) => ({
        ...prev,
        [textInputData.dataContextId || 'unknown']: {
          ...prev[textInputData.dataContextId || 'unknown'],
          value: textInputData.value,
        },
      }));
    }
    if (textInputData?.saveToSelections && textInputData.dataContextId) {
      setUserSelections((prev) => ({
        ...prev,
        [textInputData.dataContextId || 'unknown']:
          `${textInputData.dataContextLabel || 'Text'}: "${textInputData.value}"`,
      }));
    }

    setShowTextDialog(false);
    setTextInputData({
      type: 'input',
      value: '',
      dataContextId: '',
      saveToSelections: false,
      dataContextLabel: '',
    });
  };

  // Handle checkbox action
  const handleCheckboxAction = (
    dataContextId: string,
    checkboxValue: string,
    dataContextLabel?: string,
    saveToSelections?: boolean,
  ) => {
    if (!dataContextId || !checkboxValue) return;

    // Get current array data
    const currentData = dataContent[dataContextId]?.value;
    let currentArray: string[] = [];

    if (Array.isArray(currentData)) {
      currentArray = [...currentData];
    } else if (typeof currentData === 'string' && currentData) {
      // .filter(Boolean) remove empty strings
      currentArray = currentData
        .split(',')
        .map((item) => item.trim())
        .filter(Boolean);
    }

    // Toggle checkbox value
    const isSelected = currentArray.includes(checkboxValue);
    let updatedArray: string[];

    if (isSelected) {
      // Case remove data
      updatedArray = currentArray.filter((item) => item !== checkboxValue);
    } else {
      // Case add data
      updatedArray = [...currentArray, checkboxValue];
    }

    // Update dataContent
    setDataContent((prev) => ({
      ...prev,
      [dataContextId]: {
        ...prev[dataContextId],
        value: updatedArray,
      },
    }));

    // Update userSelections if needed
    if (saveToSelections) {
      const formattedValue = updatedArray.join(', ');
      setUserSelections((prev) => ({
        ...prev,
        [dataContextId]: `${dataContextLabel || 'Selection'}: ${formattedValue}`,
      }));
    }

    // Update dynamic selections for calculation
    setDynamicSelections((prev) => ({
      ...prev,
      [dataContextId]: updatedArray,
    }));
  };

  // Handle dynamic modal submit
  const handleModalSubmit = () => {
    if (!currentModalConfig) return;

    console.log('Modal submit - current values:', modalValues);

    const updatedDataContent: Record<string, any> = {};
    const updatedUserSelections: Record<string, string> = {};

    currentModalConfig.inputs.forEach((input) => {
      const value = modalValues[input.id];
      if (value !== undefined && input.dataId) {
        let formattedValue = '';

        switch (input.type) {
          case 'multiSelect':
            formattedValue = Array.isArray(value) ? value.join(', ') : '';
            updatedUserSelections[input.dataId] = `${input.label}: ${formattedValue}`;
            // Update dynamic selections for calculation
            setDynamicSelections((prev) => ({
              ...prev,
              [input.dataId]: Array.isArray(value) ? value : [],
            }));
            break;
          case 'range':
            if (Array.isArray(value)) {
              formattedValue = formatValueWithType(value, input.formatType, input.formatConfig);
            }
            updatedUserSelections[input.dataId] = `${input.label}: ${formattedValue}`;
            // Update dynamic selections for calculation
            if (Array.isArray(value)) {
              setDynamicSelections((prev) => ({
                ...prev,
                [input.dataId]: [value[0], value[1]],
              }));
            }
            break;
          case 'radio':
          case 'text':
          case 'textarea':
            formattedValue = String(value);
            updatedUserSelections[input.dataId] = `${input.label}: ${formattedValue}`;
            // Update dynamic selections for calculation
            setDynamicSelections((prev) => ({
              ...prev,
              [input.dataId]: String(value),
            }));
            break;
          default:
            formattedValue = String(value);
            updatedUserSelections[input.dataId] = `${input.label}: ${formattedValue}`;
        }

        updatedDataContent[input.dataId] = {
          value: formattedValue,
        };
      }
    });

    // Update placeholders with selected data
    setDataContent((prev) => ({
      ...prev,
      ...updatedDataContent,
    }));

    // Update user selections
    setUserSelections((prev) => ({
      ...prev,
      ...updatedUserSelections,
    }));

    setShowModal(false);
    setCurrentModalConfig(null);
    setModalValues({});
  };

  const handleClickButton = (e: React.MouseEvent<HTMLImageElement>) => {
    if (!imageRef.current) return;

    const screenElements = appSimulationScreens[currentScreenIndex]?.elements || [];
    const indexes = screenElements.map((_, index) => index);
    setHoveredElements(indexes);
  };

  // Function to get current value from placeholder by dataId
  const getCurrentValue = (dataId: string): string | number => {
    // First check if there's a time-based value
    const timeBasedValue = timeBasedValues[dataId];
    if (timeBasedValue !== undefined) {
      return timeBasedValue;
    }

    // Then check dataContent
    const dataValue = dataContent[dataId]?.value;
    if (dataValue !== undefined) {
      return dataValue;
    }

    // Finally check for placeholder initial values
    const placeholder = appSimulationScreens[currentScreenIndex].placeholders?.find(
      (p) => p.dataId === dataId,
    );
    if (placeholder?.initialValue !== undefined) {
      return placeholder.initialValue;
    }

    return 0;
  };

  const handleDropdownOptionClick = (option: AppSimulationElementDropdownOption) => {
    const { screenId = '', dataContext, dataContextId, saveToSelections, value, label } = option;
    const targetIndex = findScreenIndexById(appSimulationScreens, screenId);
    if (targetIndex !== -1) {
      setPreviousScreenIndex(currentScreenIndex);
      setCurrentScreenIndex(targetIndex);
    }

    // Save user selection if saveToSelections is true
    if (saveToSelections && dataContextId) {
      setUserSelections((prev) => ({
        ...prev,
        [dataContextId]: dataContext || label,
      }));
    }

    // Update dynamic selections for calculation
    if (dataContextId && value !== undefined) {
      setDynamicSelections((prev) => ({
        ...prev,
        [dataContextId]: value,
      }));

      setDataContent((prev) => ({
        ...prev,
        [dataContextId]: { value },
      }));
    }

    setShowDropdown(false);
  };

  return (
    <>
      <div
        className={cn(
          'flex h-full flex-col items-center justify-center',
          appSimulationScreens[currentScreenIndex].bgColor
            ? `${appSimulationScreens[currentScreenIndex].bgColor}`
            : '',
        )}
        ref={containerRef}
      >
        <div className="relative" ref={imageContainerRef}>
          <img
            ref={imageRef}
            onClick={handleClickButton}
            // className="max-h-full w-auto"
            style={{ display: 'block' }} // Ensure image is block to avoid layout issues
          />

          {/* Overlay for buttons */}
          {appSimulationScreens[currentScreenIndex].elements?.map((element, index) => (
            <div
              key={`element-${appSimulationScreens[currentScreenIndex].id}-${index}`}
              style={getElementStyles(
                { ...element, index },
                { isHovered: hoveredElements.includes(index) },
              )}
              onMouseEnter={() => setHoveredElements([index])}
              onMouseLeave={() => setHoveredElements([])}
              onClick={(e) => {
                e.stopPropagation();
                const arrayButtonAction =
                  element.actions || (element.action ? [element.action] : []) || [];
                for (const buttonAction of arrayButtonAction) {
                  if (!buttonAction) continue;
                  if (buttonAction.type === 'nextScreen') {
                    let targetIndex = -1;
                    if (buttonAction.screenId) {
                      targetIndex = findScreenIndexById(
                        appSimulationScreens,
                        buttonAction.screenId,
                      );
                    } else {
                      targetIndex = !!appSimulationScreens?.[currentScreenIndex + 1]
                        ? currentScreenIndex + 1
                        : -1;
                    }
                    if (targetIndex === -1) continue;
                    setPreviousScreenIndex(currentScreenIndex);
                    setCurrentScreenIndex(targetIndex);
                    if (buttonAction.dataContextId && buttonAction.dataContext !== undefined) {
                      setDataContent((prev) => ({
                        ...prev,
                        [buttonAction.dataContextId!]: {
                          ...prev[buttonAction.dataContextId!],
                          value: buttonAction.dataContext,
                        },
                      }));

                      if (buttonAction.saveToSelections) {
                        setUserSelections((prev) => ({
                          ...prev,
                          [buttonAction.dataContextId!]: buttonAction.dataContext!,
                        }));
                      }
                    }
                  } else if (buttonAction.type === 'previousScreen') {
                    const targetIndex = previousSreenIndex;
                    setCurrentScreenIndex(targetIndex);
                    setPreviousScreenIndex(previousSreenIndex - 1);
                    if (buttonAction.dataContextId && buttonAction.dataContext !== undefined) {
                      setDataContent((prev) => ({
                        ...prev,
                        [buttonAction.dataContextId!]: {
                          ...prev[buttonAction.dataContextId!],
                          value: buttonAction.dataContext,
                        },
                      }));

                      if (buttonAction.saveToSelections) {
                        setUserSelections((prev) => ({
                          ...prev,
                          [buttonAction.dataContextId!]: buttonAction.dataContext!,
                        }));
                      }
                    }
                  } else if (buttonAction.type === 'dropdown' && buttonAction.dropdownOptions) {
                    // Show dropdown
                    setDropdownOptions(buttonAction.dropdownOptions);
                    setDropdownPosition({
                      x: (element.x1 + element.x2) / 2,
                      y: element.y2 + 2,
                    });
                    setShowDropdown(true);
                  } else if (buttonAction.type === 'uploadPhoto') {
                    // Handle photo upload
                    handlePhotoUpload(buttonAction.dataContextId || 'image');
                  } else if (buttonAction.type === 'inputText') {
                    // Handle text input
                    handleTextInput(
                      buttonAction.inputTextType,
                      buttonAction.dataContextLabel,
                      buttonAction.dataContextId,
                      buttonAction.saveToSelections,
                    );
                  } else if (buttonAction.type === 'modal') {
                    // Handle dynamic modal
                    if (buttonAction.modalConfig) {
                      setCurrentModalConfig(buttonAction.modalConfig);
                      // Initialize modal values with current selections or defaults
                      const initialValues: Record<string, any> = {};
                      buttonAction.modalConfig.inputs.forEach((input) => {
                        // Generic initialization based on dataId
                        const currentValue = dynamicSelections[input.dataId!];
                        if (currentValue !== undefined) {
                          initialValues[input.id] = currentValue;
                        } else if (input.defaultValue !== undefined) {
                          initialValues[input.id] = input.defaultValue;
                        } else if (input.type === 'multiSelect') {
                          initialValues[input.id] = [];
                        } else if (
                          input.type === 'range' &&
                          input.min !== undefined &&
                          input.max !== undefined
                        ) {
                          initialValues[input.id] = [input.min, input.max];
                        } else {
                          initialValues[input.id] = '';
                        }
                      });
                      console.log('Modal initialized with values:', initialValues);
                      setModalValues(initialValues);
                      setShowModal(true);
                    }
                  } else if (buttonAction.type === 'image') {
                    if (buttonAction.dataContextId) {
                      setDataContent((prev) => ({
                        ...prev,
                        [buttonAction.dataContextId!]: {
                          ...prev[buttonAction.dataContextId!],
                          value: buttonAction.imgPath,
                        },
                      }));
                      if (buttonAction.saveToSelections) {
                        setUserSelections((prev) => ({
                          ...prev,
                          [buttonAction.dataContextId!]: buttonAction.dataContext!,
                        }));
                      }
                      if (buttonAction.saveToDynamicValues && buttonAction.dataContext) {
                        setDynamicSelections((prev) => ({
                          ...prev,
                          [buttonAction.dataContextId!]: buttonAction.dataContext!,
                        }));
                      }
                    }
                  } else if (buttonAction.type === 'checkbox') {
                    // Handle checkbox action
                    if (buttonAction.dataContextId && buttonAction.dataContext) {
                      handleCheckboxAction(
                        buttonAction.dataContextId,
                        buttonAction.dataContext,
                        buttonAction.dataContextLabel,
                        buttonAction.saveToSelections,
                      );
                    }
                  } else if (buttonAction.type === 'triggerMessage') {
                    // Handle trigger message
                    let finalMessage = buttonAction.message || '';

                    if (buttonAction.withData && Object.keys(userSelections).length > 0) {
                      const selectionsText = Object.entries(userSelections)
                        .map(([key, value]) => `${key}: ${value}`)
                        .join(', ');
                      finalMessage = `${finalMessage} ${selectionsText}`;
                    }

                    console.log('finalMessage ::: 2 ', finalMessage);

                    setJobsimulationTriggerMessage({
                      message: finalMessage,
                      isTriggered: true,
                    });
                  }
                }
              }}
              title={element.title}
            />
          ))}

          {/* Placeholders overlay */}
          {appSimulationScreens[currentScreenIndex].placeholders?.map((placeholder, index) => {
            // Get data content by dataId instead of screenId
            const currentContent = placeholder.dataId ? dataContent[placeholder.dataId] : undefined;
            // Check if this placeholder has time-based data
            const hasTimeBasedData = placeholder.dataByTime || placeholder.increaseByTime;
            const timeBasedValue = hasTimeBasedData
              ? (dataContent[placeholder.dataId || '']?.value ?? timeBasedValues[placeholder.id])
              : undefined;

            if (placeholder.type === 'image') {
              return (
                <PlaceholderImage
                  key={`placeholder-${placeholder.id || index}`}
                  placeholder={placeholder}
                  choosedImage={currentContent?.value}
                />
              );
            }
            if (placeholder.type === 'text' || placeholder.type === 'number') {
              let displayText = '';
              if (hasTimeBasedData && timeBasedValue) {
                displayText = timeBasedValue;
                // console.log('displayText timeBasedValue', displayText);
              } else if (placeholder.dataId && calculatedEstimates[placeholder.dataId]) {
                // Use calculated estimates for specific dataIds
                displayText = calculatedEstimates[placeholder.dataId];
                // console.log('displayText calculatedEstimates', displayText);
              } else if (currentContent?.value) {
                displayText = currentContent.value;
                // console.log('displayText currentContent', displayText);
              } else if (currentContent?.value === undefined && placeholder.initialValue) {
                displayText = placeholder.initialValue;
                // console.log('displayText initialValue', displayText);
              }

              if (placeholder.type === 'number' && displayText !== '') {
                const parsed = parseFloat(displayText.replace(/,/g, ''));
                if (!isNaN(parsed)) {
                  displayText = parsed.toLocaleString('en-US');
                }
              }

              if (placeholder.id === 'value_network') {
                const values: string[] = ['Google Search Network'];
                if (currentContent?.value?.includes('Search partners')) {
                  values.push('Search Partners');
                }
                if (currentContent?.value?.includes('Display Network')) {
                  values.push('Display Network');
                }
                displayText = values.join(', ');
              }

              const renderedContent = renderPlaceholderContent(placeholder, displayText);

              return (
                <div
                  key={`placeholder-${placeholder.id || index}`}
                  style={getPlaceholderStyle(placeholder, { containerWidth })}
                  className="flex items-center"
                >
                  {renderedContent}
                </div>
              );
            }

            if (placeholder.type === 'view') {
              return (
                <div
                  key={`placeholder-${placeholder.id || index}`}
                  className={`space-y-1 py-0.5`}
                  style={{
                    position: 'absolute',
                    left: `${placeholder.x1}%`,
                    top: `${placeholder.y1}%`,
                    height: `fit-content`,
                    width: `${placeholder.x2 - placeholder.x1}%`,
                    zIndex: 16,
                    display: 'flex',
                    borderRadius: '4px',
                    lineHeight: '0.75rem',
                    textAlign: 'left',
                    overflow: 'auto',
                    wordBreak: 'break-word',
                    whiteSpace: 'pre-line',
                    padding: '2px 0px',
                    fontWeight: 500,
                    flexDirection: 'column',
                    ...placeholder.style,
                  }}
                >
                  <p className="xxl:text-xs text-[10px] font-semibold">Sponsored</p>
                  <div className="flex items-center gap-2">
                    <span className="rounded-full bg-blue-100 p-1 text-blue-500">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="16"
                        height="16"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        className="lucide lucide-earth-icon lucide-earth"
                      >
                        <path d="M21.54 15H17a2 2 0 0 0-2 2v4.54" />
                        <path d="M7 3.34V5a3 3 0 0 0 3 3a2 2 0 0 1 2 2c0 1.1.9 2 2 2a2 2 0 0 0 2-2c0-1.1.9-2 2-2h3.17" />
                        <path d="M11 21.95V18a2 2 0 0 0-2-2a2 2 0 0 1-2-2v-1a2 2 0 0 0-2-2H2.05" />
                        <circle cx="12" cy="12" r="10" />
                      </svg>
                    </span>
                    <p className="xxl:text-xs text-[10px] font-light">
                      {dataContent?.urlInput?.value || 'www.example.com/'}
                      {dataContent?.displayPath?.value}
                    </p>
                  </div>
                  <div className="xxl:text-lg space-x-1 text-sm font-normal text-blue-500">
                    <span>{dataContent?.headline1?.value || 'Headline 1'}</span>
                    <span>-</span>
                    <span>{dataContent?.headline2?.value || 'Headline 2'}</span>
                    <span>-</span>
                    <span>{dataContent?.headline3?.value || 'Headline 3'}</span>
                  </div>
                  <div className="xxl:text-xs space-x-1 text-[10px] font-light">
                    <span>{dataContent?.desc1?.value || 'Description 1'}</span>
                    <span>-</span>
                    <span>{dataContent?.desc2?.value || 'Description 2'}</span>
                  </div>
                  <hr />
                </div>
              );
            }

            if (placeholder.type === 'overviewAds') {
              return (
                <div
                  key={`placeholder-${placeholder.id || index}`}
                  className={`space-y-1 py-0.5`}
                  style={{
                    position: 'absolute',
                    left: `${placeholder.x1}%`,
                    top: `${placeholder.y1}%`,
                    height: `${placeholder.y2 - placeholder.y1}%`,
                    width: `${placeholder.x2 - placeholder.x1}%`,
                    zIndex: 16,
                    display: 'flex',
                    lineHeight: '0.75rem',
                    textAlign: 'left',
                    overflow: 'auto',
                    wordBreak: 'break-word',
                    whiteSpace: 'pre-line',
                    padding: '8px',
                    fontWeight: 500,
                    flexDirection: 'column',
                    ...placeholder.style,
                  }}
                >
                  <div className="space-y-1 rounded-lg border p-2">
                    <div className="space-x-1 text-base font-normal text-blue-600">
                      <span>{dataContent?.headline1?.value || 'Headline 1'}</span>
                      <span>-</span>
                      <span>{dataContent?.headline2?.value || 'Headline 2'}</span>
                      <span>-</span>
                      <span>{dataContent?.headline3?.value || 'Headline 3'}</span>
                    </div>
                    <p className="text-xs font-light text-green-500">
                      <span className="rounded border border-green-500 p-0.5">Ad</span>{' '}
                      {dataContent?.urlInput?.value || 'www.example.com/'}
                      {dataContent?.displayPath?.value}
                    </p>
                    <div className="space-x-1 text-xs font-light text-gray-400">
                      <span>{dataContent?.desc1?.value || 'Description 1'}</span>
                      <span>-</span>
                      <span>{dataContent?.desc2?.value || 'Description 2'}</span>
                    </div>
                  </div>
                </div>
              );
            }

            if (placeholder.type === 'checkbox') {
              // Check if this checkbox value is selected
              const currentData = placeholder.dataId
                ? dataContent[placeholder.dataId]?.value
                : undefined;
              let isSelected = false;

              if (Array.isArray(currentData)) {
                isSelected = currentData.includes(placeholder.checkboxValue || '');
              } else if (typeof currentData === 'string' && currentData) {
                const currentArray = currentData
                  .split(',')
                  .map((item) => item.trim())
                  .filter(Boolean);
                isSelected = currentArray.includes(placeholder.checkboxValue || '');
              }

              return (
                <div
                  key={`placeholder-${placeholder.id || index}`}
                  style={getPlaceholderStyle(placeholder, {
                    isSelected,
                    containerWidth,
                  })}
                >
                  {isSelected ? '✓' : ''}
                </div>
              );
            }

            return null;
          })}

          {/* Charts overlay */}
          {appSimulationScreens[currentScreenIndex].charts?.map((chart, index) => {
            if (chart.type === 'line-time') {
              return (
                <LineChartAutoTime
                  key={`chart-${chart.id || index}`}
                  // TODO: To support legacy top,left,width,height.
                  chartArea={{
                    x1: chart.chartArea?.x1!,
                    y1: chart.chartArea?.y1!,
                    x2: chart.chartArea?.x2!,
                    y2: chart.chartArea?.y2!,
                  }}
                  data={chart.data}
                />
              );
            } else if (chart.type === 'bar') {
              return (
                <div
                  key={`placeholder-${chart.id || index}`}
                  className={`py-0.5`}
                  style={{
                    position: 'absolute',
                    left: `${chart.chartArea?.x1}%`,
                    top: `${chart.chartArea?.y1}%`,
                    height: `${(chart.chartArea?.y2 ?? 0) - (chart.chartArea?.y1 ?? 0)}%`,
                    width: `${(chart.chartArea?.x2 ?? 0) - (chart.chartArea?.x1 ?? 0)}%`,
                    zIndex: 16,
                    overflow: 'auto',
                    wordBreak: 'break-word',
                    whiteSpace: 'pre-line',
                  }}
                >
                  <MetricsBarChart />
                </div>
              );
            }
            if (chart.type === 'line-time-dynamic') {
              return (
                <LineChartDynamic
                  key={`chart-${chart.id || index}`}
                  // TODO: To support legacy top,left,width,height.
                  chartArea={{
                    x1: chart.chartArea?.x1!,
                    y1: chart.chartArea?.y1!,
                    x2: chart.chartArea?.x2!,
                    y2: chart.chartArea?.y2!,
                  }}
                  data={chart.data}
                  getCurrentValue={getCurrentValue}
                />
              );
            }
            return null;
          })}

          {/* Dropdown overlay */}
          <AppSimulationDropdown
            dropdownOptions={dropdownOptions}
            handleClickOption={handleDropdownOptionClick}
            dropdownPosition={dropdownPosition}
            showDropdown={showDropdown}
          />
        </div>
      </div>

      {/* TODO: find a way to merge DialogTextInput with Dyanimic Modal --> Use Dynamic Modal */}
      <DialogTextInput
        isOpen={showTextDialog}
        onOpenChange={setShowTextDialog}
        textInputData={textInputData}
        onInputChange={(value) => setTextInputData((prev) => ({ ...prev, value }))}
        onSubmit={handleTextDialogSubmit}
      />

      {/* Dynamic Modal */}
      <DialogDynamicInputs
        open={showModal}
        onOpenChange={setShowModal}
        handleModalSubmit={handleModalSubmit}
        modalConfig={currentModalConfig}
        setModalValues={setModalValues}
        modalValues={modalValues}
      />
    </>
  );
};

export default AppSimulation;
