import { useEffect, useRef, useState } from 'react';
import { Button, Dialog, OGDialogTemplate, Input } from '~/components/ui';

interface IProps {
  open: boolean;
  onClose: () => void;
  onEdit: (screenId: string, params: { file: File | null; name: string }) => void;
  screen: any;
  disabled?: boolean;
}

export default function EditScreenModal({
  open,
  onClose,
  onEdit,
  screen,
  disabled = false,
}: IProps) {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [screenName, setScreenName] = useState<string>('');
  const [nameError, setNameError] = useState<string>('');
  const fileInputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    if (open && screen) {
      setPreviewUrl(screen.image);
      setScreenName(screen.name || '');
      setNameError('');
    }
    return () => {
      if (previewUrl && previewUrl !== screen?.image) {
        URL.revokeObjectURL(previewUrl);
      }
    };
  }, [open, screen]);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    if (!file.type.startsWith('image/')) {
      return;
    }

    if (previewUrl && previewUrl !== screen?.image) {
      URL.revokeObjectURL(previewUrl);
    }

    setSelectedFile(file);

    const newPreviewUrl = URL.createObjectURL(file);
    setPreviewUrl(newPreviewUrl);
  };

  const handleSelectImageClick = () => {
    fileInputRef.current?.click();
  };

  const handleNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setScreenName(value);

    // Clear error when user starts typing
    if (value.trim() && nameError) {
      setNameError('');
    }
  };

  const validateForm = (): boolean => {
    if (!screenName.trim()) {
      setNameError('Screen name is required');
      return false;
    }
    return true;
  };

  const handleSubmit = () => {
    if (!validateForm()) return;

    onEdit(screen._id, { file: selectedFile, name: screenName });
  };

  const isChanged = selectedFile || screenName !== screen.name;

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <OGDialogTemplate
        className="w-full max-w-2xl"
        title="Edit Screen"
        main={
          <div className="flex flex-col gap-6">
            <div className="flex flex-col gap-1">
              <Input
                id="screen-name"
                type="text"
                value={screenName}
                onChange={handleNameChange}
                placeholder="Enter screen name"
                className={`w-full border ${nameError ? 'border-red-500' : 'border-gray-600'} bg-gray-800 text-white placeholder:text-gray-500`}
                maxLength={56}
              />
              {nameError ? (
                <div className="text-xs text-red-500">{nameError}</div>
              ) : (
                <div className="text-xs text-gray-500">
                  Descriptive name helps identify this screen in your app
                </div>
              )}
            </div>

            <div className="flex flex-col items-center justify-center gap-6 sm:flex-row">
              <div className="relative aspect-[3/4] w-full overflow-hidden rounded-lg bg-gray-900 sm:w-3/5">
                {previewUrl ? (
                  <div className="flex h-full w-full items-center justify-center">
                    <img
                      src={previewUrl}
                      alt="Screen preview"
                      className="max-h-full max-w-full object-contain"
                    />

                    {/* Overlay with replace button */}
                    <div
                      className="absolute inset-0 flex cursor-pointer flex-col items-center justify-center bg-black bg-opacity-0 transition-all hover:bg-opacity-50"
                      onClick={handleSelectImageClick}
                    >
                      <div className="scale-0 transform rounded-full bg-white bg-opacity-90 p-3 shadow-lg transition-all duration-200 hover:bg-opacity-100 group-hover:scale-100">
                        <svg
                          className="h-6 w-6 text-gray-800"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth="2"
                            d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
                          />
                        </svg>
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="flex h-full w-full items-center justify-center">
                    <p className="text-center text-gray-400">No image available</p>
                  </div>
                )}
              </div>

              <div className="flex w-full flex-col gap-3 sm:w-2/5">
                <div
                  className="group flex aspect-[4/3] w-full cursor-pointer flex-col items-center justify-center rounded-lg border-2 border-dashed border-gray-600 bg-gray-800 p-4 transition-colors hover:border-purple-500"
                  onClick={handleSelectImageClick}
                >
                  <div className="flex flex-col items-center justify-center">
                    <svg
                      className="mb-2 h-12 w-12 text-gray-500 transition-colors group-hover:text-purple-400"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
                      />
                    </svg>
                    <p className="text-center text-sm font-medium text-gray-400 transition-colors group-hover:text-white">
                      Click to replace image
                    </p>
                    <p className="mt-1 text-center text-xs text-gray-500">PNG, JPG, JPEG</p>
                  </div>
                </div>

                {selectedFile && (
                  <div className="mt-2 rounded-md border border-gray-700 bg-gray-800 p-3 text-sm text-gray-300">
                    <div className="flex items-center justify-between">
                      <p className="font-medium">Selected file:</p>
                      <span className="rounded-full bg-purple-500 bg-opacity-20 px-2 py-0.5 text-xs font-medium text-purple-300">
                        New
                      </span>
                    </div>
                    <p className="mt-1 truncate text-xs">{selectedFile.name}</p>
                    <div className="mt-2 flex items-center justify-between text-xs text-gray-400">
                      <span>{(selectedFile.size / 1024).toFixed(2)} KB</span>
                      <span>{selectedFile.type.split('/')[1].toUpperCase()}</span>
                    </div>
                  </div>
                )}

                <input
                  type="file"
                  className="hidden"
                  ref={fileInputRef}
                  accept="image/*"
                  onChange={handleFileChange}
                />
              </div>
            </div>
          </div>
        }
        buttons={
          <Button onClick={handleSubmit} disabled={disabled || !isChanged}>
            Save Changes
          </Button>
        }
      />
    </Dialog>
  );
}
