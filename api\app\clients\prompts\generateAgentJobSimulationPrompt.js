const dedent = require('dedent');
// const jobSimulationESGAnalystPrompt = require('./jobSimulationESGAnalystPrompt');
// const testJobSimulationPrompt = require('./testJobSimulationPrompt');
// const jobSimulationDigitalMarketingPrompt = require('./jobSimulationDigitalMarketingPrompt');
const { jobSimulationPrompt } = require('./jobSimulationPrompts')

const JobSimulationService = require('~/server/services/JobSimulation/JobSimulationService');

// const prompts = {
//   'esg-analyst': jobSimulationESGAnalystPrompt,
//   'digital-marketing': jobSimulationDigitalMarketingPrompt,
//   'test-prompt': testJobSimulationPrompt
// };

const generateAgentJobsimulationPrompt = async (data) => {
  const { jobSimulationId = '', email = '' } = data;
  const jobSimulation = await JobSimulationService.getAdminJobSimulation(jobSimulationId);
  const jsPrompt = jobSimulationPrompt({ virtualWorld: jobSimulation.virtualWorld, credentials: jobSimulation.credentials, intakeId: jobSimulation.billionIntakeId, email, allowFeedback: jobSimulationId === 'lucas-test' });
  return dedent`
${jsPrompt}

${jobSimulation.agentInstructions || ''}
`;
};

module.exports = generateAgentJobsimulationPrompt;
