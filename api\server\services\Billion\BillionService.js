const axios = require('axios');

const ENDPOINTS = {
  JOIN_INTAKE: '/api/v1/program-group-enrollments/join-from-ic',
  GET_TASKS: '/api/v1/user-intern-tasks/job-simulation',
  SUBMIT_TASK: '/api/v1/task-submissions/partner/job-simulation',
  VERIFY_TOKEN: '/api/v1/verify-token',
}

const BillionService = {
  async addUserToIntake({ email, password, code, firstName, lastName }) {
    // const code = getIntakeInvitationCode(jobSimulationId);
    if (!code) {
      throw new Error('No Billion Intake Code provided.');
    }
    // console.log('BillionService addUserToIntake ::: ', email, password, code, firstName, "--", lastName);

    const url = `${process.env.BILLION_API_BASE_URL}${ENDPOINTS.JOIN_INTAKE}`;
    const { data } = await axios.post(
      url,
      {
        email,
        password,
        code: [code],
        firstName,
        lastName,
      },
      {
        headers: {
          'Content-Type': 'application/json',
          Accept: 'application/json',
        },
      },
    );
    return data;
  },

  async getTasks({ email, billionIntakeId }) {
    console.log('BillionService getTasks ::: ', email, billionIntakeId);

    const url = `${process.env.BILLION_API_BASE_URL}${ENDPOINTS.GET_TASKS}?programId=${billionIntakeId}&email=${email}`;
    const { data } = await axios.get(
      url,
      {
        headers: {
          'Content-Type': 'application/json',
          Accept: 'application/json',
          'X-API-KEY': process.env.BILLION_API_KEY,
        },
      },
    );
    return data;
  },

  async submitTask({ taskId, email, billionIntakeId, content }) {
    // console.log('BillionService submitTask ::: ', taskId, email, billionIntakeId, content);

    const url = `${process.env.BILLION_API_BASE_URL}${ENDPOINTS.SUBMIT_TASK}`;
    const { data } = await axios.post(
      url,
      {
        programId: billionIntakeId,
        taskId,
        email,
        content,
      },
      {
        headers: {
          'Content-Type': 'application/json',
          Accept: 'application/json',
          'X-API-KEY': process.env.BILLION_API_KEY,
        },
      },
    );
    return data;
  },

  async verifyToken({ token, env }) {
    // console.log('BillionService verifyToken ::: ', token, env);

    const domain = env === 'prod' ? process.env.BILLION_API_BASE_URL_PROD : process.env.BILLION_API_BASE_URL;

    const url = `${domain}${ENDPOINTS.VERIFY_TOKEN}`;
    const { data } = await axios.post(
      url,
      {
        token,
        secret: env === 'prod' ? process.env.BILLION_TOKEN_SECRET_PROD : process.env.BILLION_TOKEN_SECRET,
      },
      {
        headers: {
          'Content-Type': 'application/json',
        },
      },
    );
    return data;
  }
};

module.exports = BillionService;
