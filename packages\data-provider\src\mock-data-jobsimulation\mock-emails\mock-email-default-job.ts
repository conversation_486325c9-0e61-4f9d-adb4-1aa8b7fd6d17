import { TJobSimulationEmail } from 'src/types';

const getEmails = (data: {
  jobSimulationId: string;
  logo: string;
  billionIntakeId?: string;
  companyName?: string;
}): TJobSimulationEmail[] => [
  {
    id: '1',
    name: 'HR Team',
    avatar: 'https://d14ciuzrn5ydd5.cloudfront.net/bitmeet/tutor-ai/image-3.jpg',
    role: 'HR Manager',
    email: '<EMAIL>',
    title: "Welcome to BrightWave Media's Digital Marketing Simulation!",
    desc: "We're thrilled to have you onboard for this immersive experience.",
    nextEmailId: '2',
    data: {
      logo: data.logo || '/assets/brightwave.png',
      greeting: 'Hi {user_name}',
      content: `Welcome to **BrightWave Media!** We're excited to have you join our **Digital Marketing Simulation**.

This simulation is designed to provide you with hands-on experience in digital marketing strategies, content creation, and data analysis. You'll be working on real-world tasks that reflect the dynamic nature of our industry.

To get started, please check your inbox for a meeting invitation from your manager, **<PERSON>*.

We look forward to seeing your creativity and analytical skills in action!
`,
      signature: {
        title: 'Regards',
        company: data.companyName,
      },
    },
    triggerActions: [
      {
        type: 'nextEmail',
        data: { nextEmailId: '2', triggerTimeout: 3, when: 'open' },
      },
      {
        type: 'enableApps',
        data: {
          appIds: [
            'mail',
            'news',
            'task-board',
            'meeting',
            'facebook-ad',
            'fb-ad',
            'google-ads',
            'powerbi',
          ],
          when: 'receive',
        },
      },
    ],
  },
  {
    id: '2',
    name: 'Alex Chen',
    avatar: 'https://beta.bitmeet.io/files/1748226552291-98529265-digital_marketing_onboard.jpg',
    role: 'Manager',
    email: '<EMAIL>',
    title: 'Kick-off Meeting: BrightWave Media Digital Marketing Simulation',
    desc: "Let's connect to discuss your role and upcoming tasks.",
    nextEmailId: '3',
    data: {
      logo: data.logo || '/assets/brightwave.png',
      greeting: 'Hi {user_name}',
      content: `I'm **Alex Chen**, your manager at BrightWave Media.
We're excited to kick off your Digital Marketing Simulation with an introductory meeting.
In this brief session, you'll learn about **your role and upcoming tasks**, and get set up for success.

I look forward to meeting you live and officially kicking things off together!
  `,
      actions: [
        {
          type: 'joinMeeting',
          label: 'Join the Meeting',
          title: 'Onboarding Meeting',
          data: {
            datetime: '{email_time}',
            duration: '~1 minutes',
            meetingLink: 'https://dev-bitmeet.mnet.io/introduction/642-b26-a8c?t=mi',
            // TODO: update code, listen postMessage
            // triggerAssistant: 'I have completed the meeting with Alex Chen.',
            // enableApps: ['mail', 'news', 'meeting', 'facebook-ad', 'fb-ad', 'task-board'],
            // pushNextEmail: false,

            completionMeetingActions: [
              // {
              //   type: 'triggerAssistant',
              //   data: {
              //     triggerMessage: 'I have completed the meeting with Alex Chen.',
              //   },
              // },
              {
                type: 'enableApps',
                data: {
                  appIds: [
                    'mail',
                    'news',
                    'meeting',
                    'task-board',
                    'facebook-ad',
                    'fb-ad',
                    'google-ads',
                    'powerbi',
                  ],
                },
              },
              {
                type: 'sendEmailTask',
              },
            ],
          },
        },
      ],
      signature: {
        title: 'Warm regards',
        company: data.companyName,
      },
    },
    triggerActions: [
      // {
      //   type: 'triggerAssistant',
      //   data: { triggerTimeout: 1, triggerMessage: "I've received the meeting invitation." },
      // },
      {
        type: 'triggerAssistant',
        data: {
          triggerTimeout: 1,
          triggerMessage: "I've received the meeting invitation.",
          when: 'receive',
        },
      },
      {
        type: 'enableApps',
        data: {
          appIds: ['mail', 'news', 'meeting', 'facebook-ad', 'fb-ad', 'google-ads', 'powerbi'],
          when: 'receive',
        },
      },
    ],
  },
  {
    id: '3',
    name: ' Alex Chen',
    avatar: 'https://beta.bitmeet.io/files/1748226552291-98529265-digital_marketing_onboard.jpg',
    role: 'Manager',
    email: '<EMAIL>',
    title: 'Thank You for Joining the Digital Marketing Simulation',
    desc: 'Congratulations on completing the simulation!',
    data: {
      logo: data.logo || '/assets/brightwave.png',
      greeting: 'Hi {user_name}',
      content: `Congratulations on successfully completing the Digital Marketing Simulation at BrightWave Media!
Your efforts in content planning and data analysis have been impressive.

As a token of our appreciation, we've prepared a reference letter highlighting your contributions and skills demonstrated during the simulation.

You can view your reference letter here:
  `,
      actions: [
        {
          type: 'viewFileDetail',
          label: 'View Reference Letter',
          title: 'Reference Letter',
          data: {
            fileUrl: `https://uat.internship.guru/en/public/reference-letter?programId=${data.billionIntakeId}&autoClaim=true`,
          },
        },
      ],
      signature: {
        title: 'regards',
        company: data.companyName,
      },
    },
  },
];

// Build an email task, send from manager
const buildEmailTask = (prams: { jobSimulation: any; task: any }): TJobSimulationEmail => {
  const { jobSimulation, task } = prams;
  return {
    id: `email-task:${task.taskId}`,
    type: 'task',
    name: 'Alex Chen',
    avatar: 'https://beta.bitmeet.io/files/1748226552291-98529265-digital_marketing_onboard.jpg',
    role: 'Manager',
    email: '<EMAIL>',
    title: `Task: ${task.taskName}`,
    desc: `New task assigned: ${task.taskName}`,
    data: {
      taskId: task.taskId,
      programId: jobSimulation.billionIntakeId,
      logo: jobSimulation.logo || '/assets/brightwave.png',
      greeting: 'Hi {user_name}',
      content: `You're doing great so far in the Digital Marketing Simulation at BrightWave Media!
Your next task is titled: **${task.taskName}**

${task.taskDescription}

Please review the task carefully and complete it at your earliest convenience.
**Simply reply to this email with your completed task.**

Good luck — we look forward to seeing your work!
`,
      actions: [],
      signature: {
        title: 'Best regards',
        company: jobSimulation.companyName,
      },
    },
    //       replies: [
    //         {
    //           isUser: true,
    //           datetime: Date.now() - 1000 * 60 * 60 * 24 * 2,
    //           content: `Subject: **ESG Risk Update - EcoChain Logistics**

    // Dear Team,

    // Following our review of EcoChain Logistics' ESG performance, we have identified several key areas of concern that require attention and further analysis.

    // 1. Carbon Emissions (High Risk)
    // EcoChain's fleet of over 3,000 diesel-powered trucks and a fuel mix with 90% diesel contribute to high Scope 1 emissions (140,000 tons CO₂e/year), with total emissions intensity at 1.7 kg CO₂e per package. This significantly exceeds industry norms and presents reputational and regulatory risks, especially as environmental scrutiny increases.

    // 2. Labor Practices in Subcontracted Warehouses (Medium Risk)
    // Third-party audits revealed wage violations and inadequate safety provisions in subcontracted facilities in Vietnam and Indonesia. Although efforts such as extended parental leave trials show promise, these gaps pose moderate legal and ethical concerns if unaddressed.

    // 3. Governance Gaps (Medium Risk)
    // ESG oversight currently resides with an internal group under the CFO, with no independent ESG committee and no linkage between ESG KPIs and executive pay. Disclosures remain unaudited and infrequent, reducing transparency and accountability.

    // **Recommended Actions**:
    //   - Accelerate transition to hybrid/electric fleets, prioritizing high-density urban routes.
    //   - Mandate compliance audits and labor standards enforcement across all subcontractors.
    //   - Establish an independent ESG committee and formalize KPIs tied to executive performance.

    // Best regards,
    // Lucas Vu
    // ESG Analyst, Greentek Industries`,
    //         },
    //       ],
    allowReply: true,
    triggerActions: [
      {
        type: 'triggerAssistant',
        data: {
          triggerTimeout: 1,
          triggerMessage: `I've received the task ${task.taskName}`,
          when: 'receive',
        },
      },
      {
        type: 'enableApps',
        data: {
          appIds: ['mail', 'news', 'meeting', 'facebook-ad', 'fb-ad', 'task-board'],
          when: 'receive',
        },
      },
    ],
  };
};

export default { buildEmailTask, getEmails };
