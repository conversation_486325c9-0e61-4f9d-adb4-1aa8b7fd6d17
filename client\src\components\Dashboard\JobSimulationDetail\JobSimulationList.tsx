import { dataService } from 'librechat-data-provider';
import { ChevronLeft, ChevronRight, Search, X } from 'lucide-react';
import { useCallback, useEffect, useRef, useState } from 'react';
import { useSearchParams } from 'react-router-dom';
import { Button, Dropdown, Input, Tag, TooltipAnchor } from '~/components/ui';
import { useListCandidatesQuery } from '~/data-provider/JobSimulation/queries';
import { useDebounce } from '~/hooks';

const medals = ['🥇', '🥈', '🥉'];
const skillLabels = {
  1: 'Novice',
  2: 'Basic',
  3: 'Good',
  4: 'Very Good',
  5: 'Excellent',
};

const sortOptions = [
  { value: 'scores', label: 'Scores' },
  { value: 'completionTime', label: 'Completion Time' },
];

const limitOptions = [
  { value: '5', label: '5' },
  { value: '10', label: '10' },
  { value: '20', label: '20' },
  { value: '50', label: '50' },
];

// Mock job data for search autocomplete
const mockJobsData = [
  { id: 'esg-analyst', name: 'ESG Analyst' },
  { id: 'digital-marketing', name: 'Digital Marketing Analyst' },
  // { id: 'software-engineer', name: 'Software Engineer' },
  // { id: 'data-scientist', name: 'Data Scientist' },
  // { id: 'product-manager', name: 'Product Manager' },
  // { id: 'ux-designer', name: 'UX Designer' },
  // { id: 'business-analyst', name: 'Business Analyst' },
  // { id: 'financial-analyst', name: 'Financial Analyst' },
  // { id: 'marketing-manager', name: 'Marketing Manager' },
  // { id: 'sales-representative', name: 'Sales Representative' },
  // { id: 'hr-specialist', name: 'HR Specialist' },
  // { id: 'content-writer', name: 'Content Writer' },
  // { id: 'graphic-designer', name: 'Graphic Designer' },
  // { id: 'project-manager', name: 'Project Manager' },
  // { id: 'customer-support', name: 'Customer Support Specialist' },
  // { id: 'devops-engineer', name: 'DevOps Engineer' },
  // { id: 'qa-engineer', name: 'QA Engineer' },
  // { id: 'mobile-developer', name: 'Mobile Developer' },
  // { id: 'frontend-developer', name: 'Frontend Developer' },
  // { id: 'backend-developer', name: 'Backend Developer' },
  // { id: 'fullstack-developer', name: 'Full Stack Developer' },
  // { id: 'ai-engineer', name: 'AI Engineer' },
  // { id: 'cybersecurity-analyst', name: 'Cybersecurity Analyst' },
  // { id: 'cloud-architect', name: 'Cloud Architect' },
  // { id: 'database-admin', name: 'Database Administrator' },
];

// Mock API function for job search
const searchJobsAPI = async (query: string = '', page: number = 1, limit: number = 10) => {
  // Simulate API delay
  await new Promise((resolve) => setTimeout(resolve, 300));

  // Filter and sort jobs
  const filteredJobs = mockJobsData
    .filter((job) => job.name.toLowerCase().includes(query.toLowerCase()))
    .sort((a, b) => a.name.localeCompare(b.name));

  // Pagination
  const startIndex = (page - 1) * limit;
  const endIndex = startIndex + limit;
  const paginatedJobs = filteredJobs.slice(startIndex, endIndex);

  return {
    data: paginatedJobs,
    hasMore: endIndex < filteredJobs.length,
    total: filteredJobs.length,
  };
};

const overviewStats = [
  {
    title: 'Total Job Simulations',
    value: '0',
    unit: 'Jobs',
    dataKey: 'totalJobs',
    dataType: 'number',
  },
  {
    title: 'Total Participants',
    value: '0',
    unit: 'Users',
    dataKey: 'totalParticipants',
    dataType: 'number',
  },
  {
    title: 'Total Attempts',
    value: '0',
    unit: 'Attempts',
    dataKey: 'totalAttempts',
    dataType: 'number',
  },
  {
    title: 'Total Completions',
    value: '0',
    unit: 'Simulations',
    dataKey: 'totalCompletedJobs',
    dataType: 'number',
  },
  {
    title: 'Avg. Completion Rate',
    value: '0',
    unit: '%',
    dataKey: 'avgCompletionRate',
    dataType: 'percentage',
  },
];

const jobStats = [
  {
    title: 'Total Participants',
    value: '0',
    unit: 'Users',
    dataKey: 'totalParticipants',
    dataType: 'number',
  },
  {
    title: 'Total Completions',
    value: '0',
    unit: 'Simulations',
    dataKey: 'totalCompletedJobs',
    dataType: 'number',
  },
  {
    title: 'Avg. Completion Rate',
    value: '0',
    unit: '%',
    dataKey: 'avgCompletionRate',
    dataType: 'percentage',
  },
  {
    title: 'Avg. Completion Time',
    value: '0',
    unit: '',
    dataKey: 'avgCompletionMins',
    dataType: 'time',
  },
  {
    title: 'Min. Completion Time',
    value: '0',
    unit: '',
    dataKey: 'minCompletionMins',
    dataType: 'time',
  },
];

const minutesToHours = (minutes: number) => {
  const hours = Math.floor(minutes / 60);
  const mins = minutes % 60;
  return `${hours}h ${mins}m`;
};

const buildStats = (data: any, typeStats = 'overview') => {
  return (typeStats === 'overview' ? overviewStats : jobStats).map((stat) => ({
    ...stat,
    value: formatNumber(data?.[stat.dataKey] || 0, stat.dataType || 'number'),
  }));
};

const formatNumber = (value: number, dataType: string) => {
  /**
   * TODO: format value
   * dataType = number
   *  1200 --> 1,200
   * dataType = percentage
   *  x.xx --> x.xx%
   */
  let parsedNumber = Number(value);
  switch (dataType) {
    case 'number':
      return parsedNumber.toLocaleString();
    case 'percentage':
      parsedNumber = Math.round(parsedNumber * 100) / 100;
      return `${parsedNumber}`;
    case 'time':
      return minutesToHours(Math.round(value));
    default:
      return parsedNumber.toLocaleString();
  }
};

const getOverviewDataAsync = async (params: any): Promise<any> => {
  const result = await dataService.getEmployerDashboardOverview(params);
  // const result = {
  //   data: {
  //     totalJobs: 10500,
  //     totalAttempts: 9345678123,
  //     totalCompletedJobs: 897654999,
  //     avgCompletionRate: 87.34513123,
  //     totalReferenceLetters: 86234987,
  //     totalParticipants: 654999,
  //   },
  // };
  return buildStats(result?.data || {}, 'overview');
};

const getJobOverview = async (params: any): Promise<any> => {
  const result = await dataService.getJobDetailOverview(params);
  return buildStats(result?.data?.overview || {}, 'job');
};

const JobSimulationList = () => {
  const [searchParams, setSearchParams] = useSearchParams();

  // Get initial values from URL params
  const jobSimulationId = searchParams.get('jobSimulationId');
  const jobSimulationName = searchParams.get('jobSimulationName');
  const urlPage = searchParams.get('page');
  const urlLimit = searchParams.get('limit');
  const urlOrderField = searchParams.get('orderField');
  const urlCandidateSearch = searchParams.get('candidateSearch');

  // State for candidates listing
  const [selectedJob, setSelectedJob] = useState<string | undefined>(jobSimulationId || undefined);
  const [limit, setLimit] = useState(Number(urlLimit) || 10);
  const [page, setPage] = useState(Number(urlPage) || 1);
  const [orderField, setOrderField] = useState<'scores' | 'completionTime'>(
    (urlOrderField as 'scores' | 'completionTime') || 'scores',
  );
  const [candidateSearch, setCandidateSearch] = useState(urlCandidateSearch || '');
  const [dataStats, setDataStats] = useState<any[]>([]);

  // Job search functionality
  const [jobSearchQuery, setJobSearchQuery] = useState(jobSimulationName || '');
  const [jobSearchResults, setJobSearchResults] = useState<Array<{ id: string; name: string }>>([]);
  const [isSearchingJobs, setIsSearchingJobs] = useState(false);
  const [showJobDropdown, setShowJobDropdown] = useState(false);
  const [jobSearchPage, setJobSearchPage] = useState(1);
  const [hasMoreJobs, setHasMoreJobs] = useState(false);

  // Ref for job search dropdown
  const jobSearchRef = useRef<HTMLDivElement>(null);

  // Debounce candidate search
  const debouncedCandidateSearch = useDebounce(candidateSearch, 300);

  // Update URL params only when API is called
  const updateURLParams = useCallback(() => {
    const params = new URLSearchParams();
    if (selectedJob) {
      params.set('jobSimulationId', selectedJob);
      // Find job name from results or use current query if it matches
      // TODO: what if we call API?
      const selectedJobData = mockJobsData.find((job) => job.id === selectedJob);
      if (selectedJobData) {
        params.set('jobSimulationName', selectedJobData.name);
      }
    }
    if (page !== 1) params.set('page', page.toString());
    if (limit !== 10) params.set('limit', limit.toString());
    if (orderField !== 'scores') params.set('orderField', orderField);
    if (candidateSearch) params.set('candidateSearch', candidateSearch);

    setSearchParams(params);
  }, [selectedJob, page, limit, orderField, candidateSearch, setSearchParams]);

  // Job search API simulation
  const searchJobs = async (query: string = '', loadMore: boolean = false) => {
    setIsSearchingJobs(true);

    try {
      const currentPage = loadMore ? jobSearchPage + 1 : 1;
      const result = await searchJobsAPI(query, currentPage, 10);

      if (loadMore) {
        setJobSearchResults((prev) => [...prev, ...result.data]);
        setJobSearchPage(currentPage);
      } else {
        setJobSearchResults(result.data);
        setJobSearchPage(1);
      }

      setHasMoreJobs(result.hasMore);
      setShowJobDropdown(true);
    } catch (error) {
      console.error('Error searching jobs:', error);
    } finally {
      setIsSearchingJobs(false);
    }
  };

  const handleJobSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const query = e.target.value;
    setJobSearchQuery(query);

    // TODO: check length, use debounce
    if (query.trim()) {
      searchJobs(query);
    } else {
      // TODO: load from cache
      searchJobs('');
    }
  };

  const handleJobSearchFocus = () => {
    if (jobSearchResults.length === 0) {
      searchJobs(jobSearchQuery);
    } else {
      setShowJobDropdown(true);
    }
  };

  const handleJobSearchBlur = () => {
    // Delay to allow click on dropdown items
    setTimeout(() => {
      // If user typed but didn't select a job, restore the selected job name
      if (selectedJob) {
        // TODO: Need improve because the data from API. Maybe create another local state to save the selected job name
        const selectedJobData = mockJobsData.find((job) => job.id === selectedJob);
        if (selectedJobData) {
          setJobSearchQuery(selectedJobData.name);
        }
      }
      setShowJobDropdown(false);
    }, 150);
  };

  const handleLoadMoreJobs = () => {
    if (hasMoreJobs && !isSearchingJobs) {
      searchJobs(jobSearchQuery, true);
    }
  };

  const handleClearJob = () => {
    setJobSearchQuery('');
    setSelectedJob(undefined);
    setPage(1);
    setShowJobDropdown(false);
    setJobSearchResults([]);
  };

  // React Query for candidates
  const candidatesQuery = useListCandidatesQuery(
    {
      jobSimulationId: selectedJob,
      limit,
      page,
      orderField,
      searchCandidate: debouncedCandidateSearch.length >= 3 ? debouncedCandidateSearch : undefined,
    },
    {
      enabled: true,
    },
  );

  const candidates = candidatesQuery.data?.data?.data || [];
  const meta = candidatesQuery.data?.data?.meta || { limit: 10, page: 1, totalPages: 1, total: 0 };

  // Handle candidate search input change
  const handleCandidateSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setCandidateSearch(e.target.value);
    setPage(1);
  };

  // Handle sort change
  const handleSortChange = (value: string) => {
    setOrderField(value as 'scores' | 'completionTime');
    setPage(1);
  };

  // Handle limit change
  const handleLimitChange = (value: string) => {
    setLimit(Number(value));
    setPage(1);
  };

  // Handle job selection from dropdown
  const handleJobSelect = (job: { id: string; name: string }) => {
    setJobSearchQuery(job.name);
    setShowJobDropdown(false);
    setSelectedJob(job.id);
    setPage(1); // Reset to first page when changing job
    // URL params will be updated when API is called via useEffect
    // TODO: we can/should set URL params right here
  };

  // Sync URL params only when candidates API is called
  useEffect(() => {
    if (candidatesQuery.isFetched) {
      updateURLParams();
    }
  }, [candidatesQuery.isFetched, updateURLParams]);

  // Click outside handler for job dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (jobSearchRef.current && !jobSearchRef.current.contains(event.target as Node)) {
        setShowJobDropdown(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // TODO: Merge 3 functions logic handlePreviousPage, handleNextPage, handlePageClick
  const handlePreviousPage = () => {
    if (page > 1) {
      setPage(page - 1);
    }
  };

  const handleNextPage = () => {
    if (page < meta.totalPages) {
      setPage(page + 1);
    }
  };

  const handlePageClick = (pageNumber: number) => {
    setPage(pageNumber);
  };

  useEffect(() => {
    // if (isGettingOverviewData) return;

    // setIsGettingOverviewData(true);

    if (!selectedJob) {
      getOverviewDataAsync({})
        .then((data) => {
          setDataStats(data);
        })
        .finally(() => {
          // setIsGettingOverviewData(false);
        });
      return;
    } else {
      getJobOverview({
        jobSimulationId: selectedJob,
      })
        .then((data) => {
          setDataStats(data);
        })
        .finally(() => {
          // setIsGettingOverviewData(false);
        });
      return;
    }
  }, [selectedJob]);

  // Handle click outside to close job dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (jobSearchRef.current && !jobSearchRef.current.contains(event.target as Node)) {
        setShowJobDropdown(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="mx-auto">
        <div className="grid grid-cols-12 gap-6">
          <div className="col-span-12 rounded-xl bg-white p-6 text-[#505050] drop-shadow-lg">
            {/* <div className="my-6">
              <h2 className="mb-8 text-2xl font-bold">{jobOverView?.jobSimulation?.name}</h2>
              <div className="relative rounded-lg bg-white bg-opacity-20">
                <h3 className="absolute -top-4 left-4 w-fit rounded-full bg-[#F7F7F7] px-2 py-1 text-sm">
                  Job Description
                </h3>
                <p className="rounded-lg bg-[#F7F7F7] p-4 text-sm leading-relaxed">
                  {jobOverView?.jobSimulation?.description}
                </p>
              </div>
            </div> */}
            <div className="mb-6 grid grid-cols-2 gap-2 lg:grid-cols-5">
              {dataStats.map((stat) => {
                return (
                  <div className="rounded-xl bg-[#F7F7F7] p-4" key={stat.title}>
                    <div className="mb-2 text-sm font-medium text-[#505050]">{stat.title}</div>
                    <div className="flex items-center">
                      <span className="text-2xl font-bold text-gray-900">{stat.value}</span>
                      {stat.unit ? (
                        <span className="ml-1 text-sm text-gray-500">{stat.unit}</span>
                      ) : (
                        <></>
                      )}
                    </div>
                  </div>
                );
              })}
            </div>
            <div className="mb-6 grid grid-cols-1 gap-4 md:grid-cols-3 xl:grid-cols-4">
              <div className="relative" ref={jobSearchRef}>
                <label className="mb-2 block text-sm font-medium text-[#505050]">Search Job</label>
                <div className="relative">
                  <Input
                    type="text"
                    value={jobSearchQuery}
                    onChange={handleJobSearchChange}
                    onFocus={handleJobSearchFocus}
                    onBlur={handleJobSearchBlur}
                    placeholder="Search for job simulations..."
                    className="h-10 w-full rounded-lg border border-gray-300 bg-surface-secondary px-10 py-2 text-sm placeholder:text-gray-400 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
                  />
                  <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400" />

                  {/* Right side icons */}
                  <div className="absolute right-3 top-1/2 flex -translate-y-1/2 items-center space-x-1">
                    {/* Clear button */}
                    {(jobSearchQuery || selectedJob) && (
                      <button
                        onClick={handleClearJob}
                        className="flex h-4 w-4 items-center justify-center rounded-full text-gray-400 hover:text-gray-600"
                      >
                        <X className="h-3 w-3" />
                      </button>
                    )}

                    {/* Loading spinner */}
                    {isSearchingJobs && (
                      <div className="h-4 w-4 animate-spin rounded-full border-2 border-gray-300 border-t-blue-500"></div>
                    )}
                  </div>

                  {/* Job search results dropdown */}
                  {showJobDropdown && (
                    <div className="absolute top-full z-50 mt-1 w-full rounded-lg border border-gray-200 bg-white shadow-lg">
                      <div
                        className="max-h-60 overflow-y-auto"
                        onScroll={(e) => {
                          const { scrollTop, scrollHeight, clientHeight } = e.currentTarget;
                          // Use a small threshold to account for floating point precision
                          const isAtBottom = scrollHeight - scrollTop <= clientHeight + 1;

                          if (isAtBottom && hasMoreJobs && !isSearchingJobs) {
                            console.log('Loading more jobs...', {
                              hasMoreJobs,
                              isSearchingJobs,
                              jobSearchPage,
                            });
                            handleLoadMoreJobs();
                          }
                        }}
                      >
                        {jobSearchResults.map((job) => (
                          <div
                            key={job.id}
                            className="cursor-pointer border-b border-gray-100 p-3 last:border-b-0 hover:bg-gray-50"
                            onClick={() => handleJobSelect(job)}
                          >
                            <div className="font-medium text-[#505050]">{job.name}</div>
                          </div>
                        ))}

                        {/* Loading state */}
                        {isSearchingJobs && (
                          <div className="p-3 text-center text-sm text-gray-500">
                            Loading jobs...
                          </div>
                        )}

                        {/* No results */}
                        {!isSearchingJobs && jobSearchResults.length === 0 && (
                          <div className="p-3 text-center text-sm text-gray-500">No jobs found</div>
                        )}

                        {/* Load more indicator */}
                        {!isSearchingJobs && hasMoreJobs && jobSearchResults.length > 0 && (
                          <div className="p-2 text-center text-xs text-gray-400">
                            Scroll down to load more...
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              </div>

              <div>
                <label className="mb-2 block text-sm font-medium text-[#505050]">
                  Search Candidate
                </label>
                <div className="relative">
                  <Input
                    type="text"
                    value={candidateSearch}
                    onChange={handleCandidateSearchChange}
                    placeholder="Enter name or email (3+ chars)..."
                    className="h-10 w-full rounded-lg border border-gray-300 bg-surface-secondary px-10 py-2 text-sm placeholder:text-gray-400 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
                  />
                  <div className="absolute left-3 top-1/2 -translate-y-1/2">
                    <Search className="h-4 w-4 text-gray-400" />
                  </div>
                </div>
              </div>

              <div>
                <label className="mb-2 block text-sm font-medium text-[#505050]">Sort By</label>
                <Dropdown
                  value={orderField}
                  onChange={handleSortChange}
                  options={sortOptions}
                  className="lg:w-[200px]"
                  selectorClassName="h-10 w-full justify-between"
                />
              </div>
            </div>
            {/* <hr /> */}

            {/* Loading state */}
            {candidatesQuery.isLoading && (
              <div className="flex justify-center py-8">
                <div className="text-gray-500">Loading candidates...</div>
              </div>
            )}

            {/* Error state */}
            {candidatesQuery.isError && (
              <div className="flex justify-center py-8">
                <div className="text-red-500">Error loading candidates. Please try again.</div>
              </div>
            )}

            {/* Table */}
            {!candidatesQuery.isLoading && !candidatesQuery.isError && (
              <div className="overflow-x-auto">
                <div className="flex flex-row items-center justify-end space-x-2">
                  <div className="text-sm text-gray-500">
                    Showing {(page - 1) * limit + 1} to {Math.min(page * limit, meta.total)} of{' '}
                    {meta.total} candidates
                  </div>
                  <Dropdown
                    value={limit.toString()}
                    onChange={handleLimitChange}
                    options={limitOptions}
                    className="lg:w-[100px]"
                    selectorClassName="h-10 w-full justify-between"
                  />
                </div>
                <table className="w-full">
                  <thead>
                    <tr className="border-b border-gray-200 text-sm text-[#505050] opacity-60">
                      <th className="px-4 py-3 text-left font-light">Rank</th>
                      <th className="px-4 py-3 text-left font-light">Candidate Name</th>
                      <th className="px-4 py-3 text-left font-light">Job Simulation</th>
                      <th className="px-4 py-3 text-left font-light">Skills</th>
                      <th className="px-4 py-3 text-left font-light">Note</th>
                      <th className="px-4 py-3 text-left font-light">Score</th>
                      <th className="px-4 py-3 text-left font-light">Completion Time</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-100">
                    {candidates.length === 0 ? (
                      <tr>
                        <td colSpan={7} className="px-4 py-8 text-center text-gray-500">
                          No candidates found
                        </td>
                      </tr>
                    ) : (
                      candidates.map((candidate: any, index: number) => {
                        const rank = (page - 1) * limit + index + 1;
                        return (
                          <tr key={candidate.id} className="hover:bg-gray-50">
                            <td className="px-4 py-4 text-[#505050]">
                              <span className="text-lg">{medals[rank - 1] || rank}</span>
                            </td>
                            <td className="px-4 py-4">
                              <div>
                                <p className="font-medium">{candidate.user?.name || '-'}</p>
                                <p className="text-xs font-light">
                                  {candidate.user?.email || candidate.email}
                                </p>
                              </div>
                            </td>
                            <td className="px-4 py-4 text-[#505050]">
                              <span>{candidate.jobSimulation?.name || '-'}</span>
                            </td>
                            <td className="max-w-56 px-4 py-4 text-[#505050]">
                              <div className="flex flex-wrap gap-2 text-sm font-medium text-gray-600">
                                {(candidate.skills || [])
                                  .sort((s1: any, s2: any) => s2.rating - s1.rating)
                                  .slice(0, 2)
                                  .map((skill: any) => (
                                    <Tag
                                      key={skill.name}
                                      label={skill.name}
                                      labelClassName="w-auto px-2 ml-0 truncate"
                                      title={
                                        skillLabels[skill.rating as keyof typeof skillLabels] ||
                                        'Good'
                                      }
                                    />
                                  ))}
                                {(candidate.skills || []).length > 2 && (
                                  <TooltipAnchor
                                    descriptionComponent={
                                      <div className="flex flex-wrap gap-2 text-sm font-medium text-gray-600">
                                        {(candidate.skills || []).map((skill: any) => (
                                          <div key={skill.name}>
                                            <Tag
                                              label={skill.name}
                                              labelClassName="w-auto px-2 ml-0"
                                              title={
                                                skillLabels[
                                                  skill.rating as keyof typeof skillLabels
                                                ] || 'Good'
                                              }
                                            />
                                          </div>
                                        ))}
                                      </div>
                                    }
                                    side="right"
                                    toolTipClassName="max-w-[300px]"
                                    render={<Tag label="..." labelClassName="w-auto px-2 ml-0" />}
                                  />
                                )}
                              </div>
                            </td>
                            <td className="px-4 py-4 text-[#505050]">
                              <span>{candidate.note || '-'}</span>
                            </td>
                            <td className="px-4 py-4 text-[#505050]">
                              <span>{candidate.scores || 0}</span>
                            </td>
                            <td className="px-4 py-4 text-[#505050]">
                              <span>{minutesToHours(candidate.completionMins || 0)}</span>
                            </td>
                          </tr>
                        );
                      })
                    )}
                  </tbody>
                </table>
              </div>
            )}
            {/* Pagination */}
            {!candidatesQuery.isLoading && !candidatesQuery.isError && candidates.length > 0 && (
              <div className="mt-4 flex items-center justify-end">
                <div className="flex items-center space-x-2">
                  <Button
                    size="sm"
                    className="bg-transparent hover:bg-gray-50"
                    onClick={handlePreviousPage}
                    disabled={page <= 1}
                  >
                    <ChevronLeft color="black" />
                  </Button>

                  {/* Page numbers with improved UX */}
                  {(() => {
                    const totalPages = meta.totalPages;
                    const currentPage = page;
                    const pages: React.ReactNode[] = [];

                    if (totalPages <= 4) {
                      // Show all pages if 4 or fewer
                      for (let i = 1; i <= totalPages; i++) {
                        pages.push(
                          <Button
                            key={i}
                            variant={currentPage === i ? 'default' : 'outline'}
                            size="sm"
                            onClick={() => handlePageClick(i)}
                            className="min-w-10"
                          >
                            {i}
                          </Button>,
                        );
                      }
                    } else {
                      // Always show page 1
                      pages.push(
                        <Button
                          key={1}
                          variant={currentPage === 1 ? 'default' : 'outline'}
                          size="sm"
                          onClick={() => handlePageClick(1)}
                          className="min-w-10"
                        >
                          1
                        </Button>,
                      );

                      // Show gap if needed
                      if (currentPage > 4) {
                        pages.push(
                          <span key="gap1" className="px-2 text-gray-500">
                            ...
                          </span>,
                        );
                      }

                      // Show pages around current page
                      const start = Math.max(2, currentPage - 1);
                      const end = Math.min(totalPages - 1, currentPage + 1);

                      for (let i = start; i <= end; i++) {
                        if (i !== 1 && i !== totalPages) {
                          pages.push(
                            <Button
                              key={i}
                              variant={currentPage === i ? 'default' : 'outline'}
                              size="sm"
                              onClick={() => handlePageClick(i)}
                              className="min-w-10"
                            >
                              {i}
                            </Button>,
                          );
                        }
                      }

                      // Show gap if needed
                      if (currentPage < totalPages - 3) {
                        pages.push(
                          <span key="gap2" className="px-2 text-gray-500">
                            ...
                          </span>,
                        );
                      }

                      // Always show last page
                      if (totalPages > 1) {
                        pages.push(
                          <Button
                            key={totalPages}
                            variant={currentPage === totalPages ? 'default' : 'outline'}
                            size="sm"
                            onClick={() => handlePageClick(totalPages)}
                            className="min-w-10"
                          >
                            {totalPages}
                          </Button>,
                        );
                      }
                    }

                    return pages;
                  })()}

                  <Button
                    size="sm"
                    className="bg-transparent hover:bg-gray-50"
                    onClick={handleNextPage}
                    disabled={page >= meta.totalPages}
                  >
                    <ChevronRight color="black" />
                  </Button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default JobSimulationList;
