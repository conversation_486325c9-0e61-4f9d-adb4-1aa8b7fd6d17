import { useEffect, useState } from "react";
import JobSelectionCard from "./JobSelectionCard";
import JobSimulationStats from "./JobSimulationStats";
import JobSimulationTalentsTable from "./JobSimulationTalentsTable";
import { dataService } from 'librechat-data-provider';
import { useNavigate, useParams } from "react-router-dom";

interface JobInfoProps {
	jobSimulation: {
		description: string;
		name: string;
	}
	overview: {
		totalParticipants: number;
		totalCompletedJobs: number;
		avgFeedbackScores: number;
		totalAttempts: number;
		avgCompletionMins: number;
		avgCompletionRate: number;
		minCompletionMins: number;
	}
}
const JobSimulationDetail = () => {
	const navigate = useNavigate();
	const params = useParams();
	const tab = params.tab as 'esg-analyst' | 'digital-marketing';
	const [selectedJob, setSelectedJob] = useState<'esg-analyst' | 'digital-marketing'>(tab || 'esg-analyst');
	const [isGettingData, setIsGettingData] = useState(false);
	const [jobOverView, setJobOverview] = useState<JobInfoProps | null>(null);

	const getJobOverview = async (params: any): Promise<any> => {
		const result = await dataService.getJobDetailOverview(params);
		return result?.data || []
	};

	useEffect(() => {
		if (!tab) return;
		setIsGettingData(true);
		getJobOverview({
			jobSimulationId: tab,
		})
			.then((data) => {
				setJobOverview(data);
			})
			.finally(() => {
				setIsGettingData(false);
			});
	}, [tab])

	return (
		<div className="min-h-screen bg-gray-50 p-4">
			<div className="mx-auto">
				<h1 className="text-xl font-medium text-[#505050] mb-6">JOB SIMULATION DETAIL</h1>

				<div className="grid grid-cols-12 gap-6">
					<div className="col-span-3">
						<JobSelectionCard
							onSelect={(job: string) => {
								navigate(`/job-simulation/detail/${job}`);
							}}
						/>
					</div>

					<div className="col-span-9 bg-white p-6 rounded-xl drop-shadow-lg text-[#505050]">
						<div className="mb-6">
							{isGettingData ? (
								<div className="animate-pulse space-y-4">
									<div className="h-6 bg-gray-300 rounded w-1/2" />
									<div className="bg-opacity-20 rounded-lg relative">
										<div>
											<div className="h-4 bg-gray-200 rounded mb-2" />
											<div className="h-4 bg-gray-200 rounded w-5/6 mb-2" />
											<div className="h-4 bg-gray-200 rounded w-4/6" />
										</div>
									</div>
								</div>
							) : (
								<>
									<h2 className="text-2xl font-bold mb-8">{jobOverView?.jobSimulation?.name}</h2>
									<div className="bg-white bg-opacity-20 rounded-lg relative">
										<h3 className="bg-[#F7F7F7] rounded-full w-fit text-sm px-2 py-1 absolute -top-4 left-4">Job Description</h3>
										<p className="text-sm leading-relaxed bg-[#F7F7F7] rounded-lg p-4">
											{jobOverView?.jobSimulation?.description}
										</p>
									</div>
								</>
							)}
						</div>
						{isGettingData ? (
							<div className="animate-pulse space-y-4 mb-6">
								<div className="h-6 bg-gray-300 rounded w-1/4 mb-2" />
								<div className="grid grid-cols-5 gap-4">
									{[...Array(5)].map((_, index) => (
										<div key={index} className="h-20 bg-gray-200 rounded-xl" />
									))}
								</div>
							</div>
						) : (
							<JobSimulationStats overview={jobOverView?.overview || null} />
						)}
						<hr />
						<JobSimulationTalentsTable selectedJob={selectedJob} />
					</div>
				</div>
			</div>
		</div>
	);
};

export default JobSimulationDetail;