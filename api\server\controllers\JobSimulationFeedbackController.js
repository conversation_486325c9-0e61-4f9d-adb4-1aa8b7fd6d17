const JobSimulationFeedbackService = require('~/server/services/JobSimulation/JobSimulationFeedbackService.js');

const JobSimulationFeedbackController = {
  async create(req, res) {
    try {
      const data = await JobSimulationFeedbackService.create(req.body);
      res.json(data);
    } catch (error) {
      console.error('=== ERROR ===', error);
      res.status(500).json({ message: error.message || 'Failed to create feedback' });
    }
  },

  async update(req, res) {
    const { params, body } = req;
    try {
      const data = await JobSimulationFeedbackService.update(params.id, body);
      res.json(data);
    } catch (error) {
      console.error('=== ERROR ===', error);
      res.status(500).json({ message: error.message || 'Failed to update feedback' });
    }
  },

  async deleteById(req, res) {
    const { params, body } = req;
    try {
      const data = await JobSimulationFeedbackService.deleteById(params.id, body.userId);
      res.json(data);
    } catch (error) {
      console.error('=== ERROR ===', error);
      res.status(500).json({ error: 'Failed to delete feedback' });
    }
  },

  async getByUserAndJobSimulation(req, res) {
    const { query } = req;
    try {
      const data = await JobSimulationFeedbackService.getByUserAndJobSimulation({
        userId: query.userId,
        jobSimulationId: query.jobSimulationId,
      });
      res.json(data);
    } catch (error) {
      console.error('=== ERROR ===', error);
      res.status(500).json({ error: 'Failed to get feedback' });
    }
  },

  async getByJobSimulation(req, res) {
    const { query } = req;
    try {
      const data = await JobSimulationFeedbackService.getByJobSimulation({
        jobSimulationId: query.jobSimulationId,
      });
      res.json(data);
    } catch (error) {
      console.error('=== ERROR ===', error);
      res.status(500).json({ error: 'Failed to get feedbacks' });
    }
  },

  async getById(req, res) {
    const { params } = req;
    try {
      const data = await JobSimulationFeedbackService.getById(params.id);
      res.json(data);
    } catch (error) {
      console.error('=== ERROR ===', error);
      res.status(500).json({ error: 'Failed to get feedback' });
    }
  },
};

module.exports = JobSimulationFeedbackController;
