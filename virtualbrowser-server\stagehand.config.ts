import type { ConstructorParams } from "@browserbasehq/stagehand";
import dotenv from "dotenv";
import path from "path";
import { fileURLToPath } from "url";

// Get current directory in ES module
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load .env from root directory
dotenv.config({ path: path.resolve(__dirname, '.env') });

const StagehandConfig: ConstructorParams = {
  // verbose: 2 /* Verbosity level for logging: 0 = silent, 1 = info, 2 = all */,
  // logger: (message) => {
  //   console.log("::: LOG STAGEHAND ::: ", message);
  // },
  verbose: 1 /* Verbosity level for logging: 0 = silent, 1 = info, 2 = all */,
  domSettleTimeoutMs: 30_000 /* Timeout for DOM to settle in milliseconds */,

  // LLM configuration
  modelName: "openai/gpt-4o-mini" /* Name of the model to use */,
  modelClientOptions: {
    apiKey: process.env.OPENAI_API_KEY,
  } /* Configuration options for the model client */,

  // Browser configuration
  env: (process.env.STAGEHAND_ENV as 'LOCAL' | 'BROWSERBASE' | undefined) || 'LOCAL' /* Environment to run in: LOCAL or BROWSERBASE */,
  apiKey: process.env.BROWSERBASE_API_KEY /* API key for authentication */,
  projectId: process.env.BROWSERBASE_PROJECT_ID /* Project identifier */,
  // browserbaseSessionID: undefined /* Session ID for resuming Browserbase sessions */,
  browserbaseSessionCreateParams: {
    projectId: process.env.BROWSERBASE_PROJECT_ID!,
    region: 'ap-southeast-1',
    browserSettings: {
      blockAds: true,
      viewport: {
        width: 1024,
        height: 768,
      },
    },
  },
  localBrowserLaunchOptions: {
    headless: true, // Launches the browser in headless mode.
    // executablePath: 'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe', // Custom path to the Chrome executable.
    args: ['--no-sandbox', '--disable-setuid-sandbox'], // Additional launch arguments.
    env: { NODE_ENV: "production" }, // Environment variables for the browser process.
    handleSIGHUP: true,
    handleSIGINT: true,
    handleSIGTERM: true,
    ignoreDefaultArgs: false, // or specify an array of arguments to ignore.
    // proxy: {
    //   server: 'http://proxy.example.com:8080',
    //   bypass: 'localhost',
    //   username: 'user',
    //   password: 'pass'
    // },
    // tracesDir: '/path/to/traces', // Directory to store trace files.
    // userDataDir: '/path/to/user/data', // Custom user data directory.
    acceptDownloads: true,
    // downloadsPath: '/path/to/downloads',
    // extraHTTPHeaders: { 'User-Agent': 'Custom Agent' },
    geolocation: { latitude: 37.7749, longitude: -122.4194, accuracy: 10 },
    permissions: ["geolocation", "notifications"],

    locale: "en-US",
    viewport: { width: 1280, height: 720 },
    deviceScaleFactor: 1,
    hasTouch: false,
    ignoreHTTPSErrors: true,
    // recordVideo: { dir: '/path/to/videos', size: { width: 1280, height: 720 } },
    // recordHar: {
    //   path: '/path/to/har.har',
    //   mode: "full",
    //   omitContent: false,
    //   content: "embed",
    //   urlFilter: '.*'
    // },
    chromiumSandbox: true,
    devtools: false,
    bypassCSP: false,
    // cdpUrl: 'http://localhost:9222',
    preserveUserDataDir: false, // Whether to preserve the user data directory after Stagehand is closed. Defaults to false.
  } /* Configuration options for the local browser */,
};

export default StagehandConfig;
