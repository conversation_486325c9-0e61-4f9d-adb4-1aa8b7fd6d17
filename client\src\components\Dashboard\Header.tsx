import { useRecoilValue } from 'recoil';
import store from '~/store';

const Header = () => {
  const user = useRecoilValue(store.user);
  const pathSegments = location.pathname;
  console.log('pathSegments ::: ', pathSegments);
  const activeTab = pathSegments.includes('overview') ? 'Overview' : 'Job Simulation Candidates';
  return (
    <div className="sticky top-0 z-30 flex items-center justify-end bg-white px-10 py-3 drop-shadow-lg">
      {/* <h2 className="text-xl font-medium uppercase opacity-50">{activeTab}</h2> */}
      <div className="flex items-center gap-3">
        <img
          src={
            user?.avatar ||
            'https://images.icon-icons.com/2859/PNG/512/avatar_face_man_boy_profile_smiley_happy_people_icon_181659.png'
          }
          className="h-10 w-10 rounded-full border"
        />
        <div>
          <p className="text-sm font-semibold">{user?.name}</p>
          {/* <p className="text-xs">{user?.role}</p> */}
        </div>
      </div>
    </div>
  );
};

export default Header;
