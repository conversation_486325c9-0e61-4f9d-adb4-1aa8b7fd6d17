import { Star } from "lucide-react";
import { StarIcon } from "~/components/svg";
import { Avatar, AvatarFallback, AvatarImage, Card, CardContent } from "~/components/ui";

interface TestimonialCardProps {
	name: string;
	avatar: string;
	rating: number;
	testimonial: string;
}

const TestimonialCard = ({ name, avatar, rating, testimonial }: TestimonialCardProps) => {
	return (
		<Card className="bg-gray-50 border-0 shadow-none rounded-xl">
			<CardContent className="p-6 text-center">
				<div className="flex flex-col items-center space-y-2">
					<Avatar className="w-16 h-16">
						<AvatarImage src={avatar} alt={name} />
						<AvatarFallback className="bg-gray-300 text-gray-600">
							{name?.charAt(0)}
						</AvatarFallback>
					</Avatar>
					<div>
						<h3 className="text-lg mb-1">{name}</h3>
						<div className="flex space-x-1 justify-center">
							{[...Array(rating)].map((_, index) => (
								<StarIcon key={index} />
							))}
						</div>
					</div>
					<p className="text-gray-600 leading-relaxed italic line-clamp-4">
						"{testimonial}"
					</p>
				</div>
			</CardContent>
		</Card>
	);
};

export default TestimonialCard;