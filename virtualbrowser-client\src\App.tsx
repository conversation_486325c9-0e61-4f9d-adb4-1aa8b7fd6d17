import { useSearchParams } from 'react-router-dom';
import BrowserStream from './components/BrowserStream';


function App() {
    const [searchParams] = useSearchParams();
    const browserUrl = searchParams.get('browserUrl');
    const userId = searchParams.get('userId') || Date.now().toString();
    console.log("Init Browser with Data ... ::: ", browserUrl, userId);

    return (
        // <BrowserStream wsUrl="ws://localhost:3001/ws" browserUrl={browserUrl || 'https://google.com'} userId={userId} />
        <BrowserStream wsUrl="wss://dev-browser-api.agentos.cloud/ws" browserUrl={browserUrl || 'https://google.com'} userId={userId} />
    )
}

export default App
