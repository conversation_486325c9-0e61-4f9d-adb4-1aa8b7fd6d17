import { TJobSimulationEmail } from 'src/types';
import { default as defaultEmailService } from './mock-email-default-job';
import { default as getBrightwaveEmails } from './mock-email-digital-marketing-analyst';
import { default as getESGAnalystEmails } from './mock-email-esg-analyst';
import { default as mobileTestingEmailService } from './mock-email-mobile-testing';
import { default as cloudArchitectEmailService } from './mock-email-cloud-architect';

export const getJobSimulationEmails = (data: {
  jobSimulationId: string;
  logo: string;
  billionIntakeId?: string;
}): TJobSimulationEmail[] => {
  try {
    if (['esg-analyst'].includes(data.jobSimulationId)) {
      return getESGAnalystEmails.getEmails(data);
    }
    if (['digital-marketing'].includes(data.jobSimulationId)) {
      return getBrightwaveEmails.getEmails(data);
    }
    if (['mobile-testing'].includes(data.jobSimulationId)) {
      return mobileTestingEmailService.getEmails(data);
    }
    if (['cloud-architect'].includes(data.jobSimulationId)) {
      return cloudArchitectEmailService.getEmails(data);
    }

    return defaultEmailService.getEmails(data);
  } catch (error) {
    console.log('Error get emails ::: ', error);
    return [];
  }
};

export const getJobSimulationEmailTask = (data: { jobSimulation: any; task: any }) => {
  try {
    const jobSimulationId = data.jobSimulation.jobSimulationId;
    if (['esg-analyst'].includes(jobSimulationId)) {
      return getESGAnalystEmails.buildEmailTask(data);
    }
    if (['digital-marketing'].includes(jobSimulationId)) {
      return getBrightwaveEmails.buildEmailTask(data);
    }
    if (['mobile-testing'].includes(jobSimulationId)) {
      return mobileTestingEmailService.buildEmailTask(data);
    }
    if (['cloud-architect'].includes(jobSimulationId)) {
      return cloudArchitectEmailService.buildEmailTask(data);
    }
    return defaultEmailService.buildEmailTask(data);
  } catch (error) {
    return null;
  }
};
