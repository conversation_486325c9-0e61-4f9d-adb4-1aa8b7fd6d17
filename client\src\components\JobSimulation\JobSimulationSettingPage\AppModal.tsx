import { useEffect, useRef, useState } from 'react';
import { Button, Dialog, Input, SelectDropDown, OGDialogTemplate } from '~/components/ui';
import { useForm, FormProvider, Controller, useWatch } from 'react-hook-form';
import type t from 'librechat-data-provider';
import { PlusIcon } from '~/components/svg';
import AvatarEditor from 'react-avatar-editor';

interface IProps {
  open: boolean;
  onClose: () => void;
  app?: t.TJobSimulationApp | null;
  type: string;
  onOk: (type: string, data) => void;
  disabled?: boolean;
}

const LIMIT_FILE_DIMENSION = 100;

export default function AppModal({ open, onClose, app, type, onOk, disabled }: IProps) {
  const [selectedIcon, setSelectedIcon] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [needsCropping, setNeedsCropping] = useState(false);
  const [imageSize, setImageSize] = useState<{ width: number; height: number } | null>(null);
  const [zoom, setZoom] = useState(0.6);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const editorRef = useRef<AvatarEditor>(null);

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    clearErrors,
  } = useForm({
    defaultValues: {
      name: type === 'edit' && app ? app.name : '',
      file: undefined as unknown as File,
    },
  });

  const checkImageSize = (url: string): Promise<{ width: number; height: number }> => {
    return new Promise((resolve) => {
      const img = new Image();
      img.onload = () => {
        resolve({ width: img.width, height: img.height });
      };
      img.src = url;
    });
  };

  const handleIconChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    if (!file.type.startsWith('image/')) {
      setValue('file', undefined as unknown as File);
      setSelectedIcon(null);
      setPreviewUrl(null);
      return;
    }

    setSelectedIcon(file);
    setValue('file', file);
    clearErrors('file');

    const reader = new FileReader();
    reader.onloadend = async () => {
      const imageUrl = reader.result as string;
      const size = await checkImageSize(imageUrl);
      setImageSize(size);

      const requiresCropping =
        size.width > LIMIT_FILE_DIMENSION || size.height > LIMIT_FILE_DIMENSION;
      setNeedsCropping(requiresCropping);

      setPreviewUrl(imageUrl);
    };
    reader.readAsDataURL(file);
  };

  const handleIconClick = () => {
    fileInputRef.current?.click();
  };

  const handleCrop = (): Promise<{ file?: File }> => {
    return new Promise((resolve) => {
      if (editorRef.current) {
        const canvas = editorRef.current.getImageScaledToCanvas();

        canvas.toBlob(
          (blob) => {
            if (blob) {
              const croppedFile = new File([blob], selectedIcon?.name || 'cropped-image.png', {
                type: 'image/png',
              });

              setSelectedIcon(croppedFile);
              setValue('file', croppedFile);
              setPreviewUrl(canvas.toDataURL());
              resolve({ file: croppedFile });
            } else {
              resolve({ file: undefined });
            }
          },
          'image/png',
          1.0,
        );
      } else {
        resolve({ file: undefined });
      }
    });
  };

  const onSubmit = async (data: any) => {
    if (needsCropping && editorRef.current) {
      const { file } = await handleCrop();

      if (typeof onOk === 'function') {
        onOk(type, { ...data, file });
      }
    } else {
      if (typeof onOk === 'function') {
        onOk(type, data);
      }
    }
  };

  useEffect(() => {
    if (type === 'edit' && app) {
      setValue('name', app.name || '');
      setSelectedIcon(app.icon ? new File([app.icon], 'icon.png') : null);
      setPreviewUrl(app.icon || null);
    }
  }, [type, app]);

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <OGDialogTemplate
        className="w-full max-w-xl"
        title={`${type === 'edit' ? 'Edit' : 'Create New'} App`}
        main={
          <div className="flex flex-col gap-4 dark:text-white">
            <div>
              <Input
                type="text"
                placeholder="Enter name"
                className="w-full border border-[#424242]"
                {...register('name', {
                  required: 'Name is required',
                  maxLength: {
                    value: 56,
                    message: 'Name cannot exceed 56 characters',
                  },
                })}
              />
              {errors?.name && (
                <span className="text-xs text-red-500">{errors?.name?.message}</span>
              )}
            </div>
            <div>
              <label className="mb-2 block text-sm font-medium">
                App Icon <span className="text-red-500">*</span>
              </label>

              {needsCropping && selectedIcon ? (
                // Image Cropping Interface
                <div className="flex flex-col gap-4">
                  <p className="text-sm text-gray-300">
                    Your image is larger than {LIMIT_FILE_DIMENSION}x{LIMIT_FILE_DIMENSION} pixels.
                    Adjust the zoom and position, then click OK to save.
                  </p>
                  <div className="flex justify-center">
                    <div className="overflow-hidden rounded-lg">
                      <AvatarEditor
                        ref={editorRef}
                        image={selectedIcon}
                        width={LIMIT_FILE_DIMENSION}
                        height={LIMIT_FILE_DIMENSION}
                        border={20}
                        borderRadius={0}
                        color={[0, 0, 0, 0.6]}
                        scale={zoom}
                      />
                    </div>
                  </div>
                  <div className="w-full">
                    <label className="text-sm">Zoom:</label>
                    <input
                      type="range"
                      min="0.6"
                      max="3"
                      step="0.1"
                      value={zoom}
                      onChange={(e) => {
                        setZoom(parseFloat(e.target.value));
                      }}
                      className="w-full"
                    />
                  </div>
                  <div className="mt-2 flex justify-between">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleIconClick}
                      className="border-gray-600 text-xs text-gray-300 hover:bg-gray-700"
                    >
                      Choose Different Image
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setNeedsCropping(false)}
                      className="border-gray-600 text-xs text-gray-300 hover:bg-gray-700"
                    >
                      Cancel Cropping
                    </Button>
                  </div>
                  <input
                    type="file"
                    className="hidden"
                    accept="image/*"
                    onChange={handleIconChange}
                    ref={fileInputRef}
                  />
                </div>
              ) : (
                <>
                  <div
                    className="flex h-32 w-32 cursor-pointer flex-col items-center justify-center rounded-lg border border-dashed border-gray-400 bg-gray-800 hover:border-purple-500"
                    onClick={handleIconClick}
                  >
                    {previewUrl ? (
                      <img
                        src={previewUrl}
                        alt="Icon preview"
                        className="cover h-16 w-16 rounded-lg"
                      />
                    ) : (
                      <>
                        <PlusIcon className="mb-2 h-8 w-8 text-gray-400" />
                        <p className="text-xs text-gray-400">Click to upload icon</p>
                        <p className="mt-1 text-xs text-gray-500">PNG, JPG, SVG</p>
                      </>
                    )}
                    <input
                      type="file"
                      className="hidden"
                      accept="image/*"
                      {...register('file', {
                        required: type === 'edit' ? false : 'Icon is required',
                      })}
                      onChange={handleIconChange}
                      ref={fileInputRef}
                    />
                  </div>
                  {errors?.file && (
                    <span className="mt-1 block text-xs text-red-500">{errors?.file?.message}</span>
                  )}
                  {selectedIcon && (
                    <p className="mt-2 text-xs text-gray-400">
                      Selected: {selectedIcon.name} ({Math.round(selectedIcon.size / 1024)} KB)
                      {imageSize &&
                        (imageSize.width > LIMIT_FILE_DIMENSION ||
                          imageSize.height > LIMIT_FILE_DIMENSION) && (
                          <span className="ml-2 text-yellow-400">
                            (Will be resized to {LIMIT_FILE_DIMENSION}x{LIMIT_FILE_DIMENSION})
                          </span>
                        )}
                    </p>
                  )}
                </>
              )}
            </div>
          </div>
        }
        buttons={
          <div>
            <Button onClick={handleSubmit(onSubmit)} disabled={disabled}>
              OK
            </Button>
          </div>
        }
      />
    </Dialog>
  );
}
