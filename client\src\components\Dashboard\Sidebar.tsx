import * as Ariakit from '@ariakit/react';
import { Home, LogOut, User } from 'lucide-react';
import { useContext, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { useRecoilValue } from 'recoil';
import type * as t from '~/common';
import { DropdownMenuSeparator, DropdownPopup } from '~/components/ui';
import { ThemeContext, useAuthContext, useMediaQuery } from '~/hooks';
import useAvatar from '~/hooks/Messages/useAvatar';
import store from '~/store';
import { cn } from '~/utils';

function UserProfileDropdown({ className }: { className?: string }) {
  const [isPopoverActive, setIsPopoverActive] = useState(false);
  const { theme, setTheme } = useContext(ThemeContext);
  const { user, logout } = useAuthContext();
  const isSmallScreen = useMediaQuery('(max-width: 768px)');
  const menuId = 'user-profile-menu';

  const avatarSrc = useAvatar(user);
  const avatarSeed = user?.avatar || user?.name || user?.username || '';

  const themeHandler = () => {
    const nextTheme = theme === 'dark' ? 'light' : 'dark';
    setTheme(nextTheme);
    setIsPopoverActive(false);
  };

  const logoutHandler = () => {
    logout();
  };

  const dropdownItems: t.MenuItemProps[] = [
    {
      // label: user?.email,
      render: (_props) => (
        <div className="p-2">
          {user?.email}
          <DropdownMenuSeparator />
        </div>
      ),
      hideOnClick: false,
    },
    // {
    //   label: theme === 'dark' ? 'Light Theme' : 'Dark Theme',
    //   onClick: themeHandler,
    //   icon: {
    //     dark: <Sun className="icon-md mr-2 text-text-secondary" />,
    //     light: <Moon className="icon-md mr-2 text-text-secondary" />,
    //   }[theme],
    //   hideOnClick: true,
    // },
    {
      label: 'Logout',
      onClick: logoutHandler,
      icon: <LogOut className="icon-md mr-2 text-text-secondary" />,
      hideOnClick: true,
    },
  ];

  return (
    <DropdownPopup
      menuId={menuId}
      focusLoop={true}
      isOpen={isPopoverActive}
      setIsOpen={setIsPopoverActive}
      trigger={
        <Ariakit.MenuButton
          id="user-profile-button"
          aria-label="User profile options"
          className={cn(
            'flex items-center gap-2 rounded-lg bg-white p-2 text-text-primary shadow-sm transition-all ease-in-out hover:bg-surface-tertiary dark:bg-gray-700 dark:text-white',
            className,
          )}
        >
          <div className="relative h-8 w-8 overflow-hidden rounded-full">
            {!avatarSeed ? (
              <div
                className="relative flex h-full w-full items-center justify-center rounded-full bg-primary/10 p-1 text-text-primary"
                aria-hidden="true"
              >
                <svg
                  stroke="currentColor"
                  fill="none"
                  strokeWidth="2"
                  viewBox="0 0 24 24"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="h-5 w-5"
                  height="1em"
                  width="1em"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                  <circle cx="12" cy="7" r="4"></circle>
                </svg>
              </div>
            ) : (
              <img
                className="h-full w-full rounded-full object-cover"
                src={(user?.avatar ?? '') || avatarSrc}
                alt={`${user?.name || user?.username || user?.email || ''}'s avatar`}
              />
            )}
          </div>
          <span className="hidden text-sm md:block">{user?.name ?? user?.username ?? 'User'}</span>
        </Ariakit.MenuButton>
      }
      items={dropdownItems}
      className={isSmallScreen ? '' : 'absolute bottom-0 left-0'}
    />
  );
}

const Sidebar = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const user = useRecoilValue(store.user);
  const pathSegments = location.pathname.split('/').filter(Boolean);
  const activeTab = pathSegments.includes('overview') ? 'overview' : 'detail';
  const menuItems = [
    {
      id: 'overview',
      label: 'Dashboard',
      icon: Home,
      isActive: pathSegments.includes('overview'),
      path: '/job-simulation/dashboard/overview',
    },
    // {
    //   id: 'jobs',
    //   label: 'Jobs',
    //   icon: Briefcase,
    //   isActive: pathSegments.includes('jobs'),
    //   path: '/job-simulation/dashboard/jobs',
    // },
    {
      id: 'candidates',
      label: 'Candidates',
      icon: User,
      isActive: pathSegments.includes('candidates'),
      path: '/job-simulation/dashboard/candidates',
    },
  ];

  return (
    <div className="flex h-screen w-64 flex-col border-r border-gray-200 bg-white text-[#505050] shadow-sm">
      <div className="flex-1">
        <h2 className="py-4 text-center text-xl font-bold text-[#34A1F4]">JOB SIMULATION</h2>
        <hr />
        <nav className="flex-1 space-y-2 py-6 pl-4">
          {menuItems.map((item) => {
            const Icon = item.icon;
            return (
              <button
                key={item.id}
                onClick={() => {
                  navigate(item.path);
                }}
                className={cn(
                  'relative flex w-full items-center gap-3 rounded-bl-lg rounded-tl-lg px-3 py-2 text-left transition-colors',
                  item.isActive ? 'font-semibold text-[#34A1F4]' : 'text-gray-600 hover:bg-gray-50',
                )}
              >
                <Icon opacity={item.isActive ? '1' : '0.2'} color='#34A1F4' size={24} />
                {item.label}
                {item.isActive && (
                  <div className="absolute bottom-0 right-0 top-0 w-1 rounded-l-sm bg-[#34A1F4]" />
                )}
              </button>
            );
          })}
        </nav>
      </div>

      <div className="w-full space-y-2 border-t p-4">
        {/* <button
					onClick={() => onTabChange('settings')}
					className={cn(
						"w-full flex items-center px-3 py-2 text-left transition-colors gap-3 hover:bg-gray-50 rounded-tl-lg rounded-bl-lg relative"
					)}
				>
					<SettingIcon opacity={activeTab === 'settings' ? '1' : '0.2'} />
					Settings
					{activeTab === 'settings' && (
						<div className="absolute right-0 top-0 bottom-0 w-1 bg-[#34A1F4] rounded-l-sm" />
					)}
				</button> */}
        {/* <button
          className={cn(
            'flex w-full items-center gap-3 rounded-bl-lg rounded-tl-lg px-3 py-2 text-left text-[#F2433A] transition-colors hover:bg-gray-50',
          )}
        >
          <LogoutJSIcon />
          Log Out
        </button> */}
        {/* <div className="flex items-center gap-3">
          <img
            src={
              user?.avatar ||
              'https://images.icon-icons.com/2859/PNG/512/avatar_face_man_boy_profile_smiley_happy_people_icon_181659.png'
            }
            className="h-10 w-10 rounded-full border"
          />
          <div>
            <p className="text-sm font-semibold">{user?.name}</p>
          </div>
        </div> */}
        <UserProfileDropdown className="w-full" />
      </div>
    </div>
  );
};

export default Sidebar;
