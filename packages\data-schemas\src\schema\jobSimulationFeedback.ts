import { Document, Schema } from 'mongoose';

export interface IJobSimulationFeedback extends Document {
  email: string;
  jobSimulationId: string;
  userId: Schema.Types.ObjectId;
  scores: number;
  content?: string;
}

const jobSimulationFeedbackSchema: Schema<IJobSimulationFeedback> = new Schema(
  {
    email: {
      type: String,
      required: true,
    },
    jobSimulationId: {
      type: String,
      required: true,
    },
    userId: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true,
    },
    scores: {
      type: Number,
      required: true,
      min: [0, 'Score cannot be less than 0'],
      max: [5, 'Score cannot be more than 5'],
      validate: {
        validator: function (value) {
          return Number.isInteger(value) && value >= 0 && value <= 5;
        },
        message: (props) =>
          `${props.value} is not a valid score. Score must be an integer between 0 and 5.`,
      },
    },
    content: {
      type: String,
      required: false,
    },
  },
  {
    timestamps: true,
  },
);

export default jobSimulationFeedbackSchema;
