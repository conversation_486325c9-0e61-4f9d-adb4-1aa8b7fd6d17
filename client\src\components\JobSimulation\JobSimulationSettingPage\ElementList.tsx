import { useState } from 'react';

import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  PopoverTrigger,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '~/components/ui';

export default function ElementList({
  elements,
  handleCopyElement,
  setElement,
  setTempElement,
  screen,
  setIsNamingModalOpen,
  handleDeleteElement,
  title = 'Element List',
  actionType = { value: 'elements', label: 'Element' },
}) {
  const [openDeletePopover, setOpenDeletePopover] = useState<string | null>(null);

  const hasElements = Array.isArray(elements) && elements.length > 0;

  return (
    <div>
      <h3 className="mb-2 font-medium">{title}</h3>

      <div className="overflow-hidden rounded-md border border-[#424242]">
        <Table className="w-full text-sm">
          <TableHeader className="">
            <TableRow>
              <TableHead className="p-2 text-left">ID</TableHead>
              <TableHead className="p-2 text-left">Name</TableHead>
              <TableHead className="p-2 text-left">Coordinates</TableHead>
              <TableHead className="p-2 text-left">Position</TableHead>
              <TableHead className="p-2 text-left">Size</TableHead>
              <TableHead className="p-2 text-right">Action</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {hasElements ? (
              elements.map((element) => (
                <TableRow key={element.id || element._id} className="border-t text-black dark:text-white">
                  <TableCell className="p-2">{element.id || element._id}</TableCell>
                  <TableCell className="p-2">{element.title || '-'}</TableCell>
                  <TableCell className="p-2">
                    ({element.x1.toFixed(1)},{element.y1.toFixed(1)}) - ({element.x2.toFixed(1)},
                    {element.y2.toFixed(1)})
                  </TableCell>
                  <TableCell className="p-2">
                    left: {element.x1.toFixed(1)}%, top: {element.y1.toFixed(1)}%
                  </TableCell>
                  <TableCell className="p-2">
                    {(element.x2 - element.x1).toFixed(2)}% x {(element.y2 - element.y1).toFixed(2)}
                    %
                  </TableCell>
                  <TableCell className="p-2 text-right">
                    <div className="flex justify-end gap-2">
                      <button
                        className="text-green-500 hover:text-green-700"
                        onClick={() => handleCopyElement(element.id)}
                      >
                        Copy
                      </button>
                      <button
                        className="text-green-300 hover:text-green-400"
                        onClick={() => {
                          setElement({
                            screenBgColor: screen.bgColor,
                            ...element,
                            actionType,
                          });
                          setTempElement({
                            id: element.id || element._id,
                            x1: element.x1,
                            y1: element.y1,
                            x2: element.x2,
                            y2: element.y2,
                          });
                          setIsNamingModalOpen(true);
                        }}
                      >
                        Edit
                      </button>
                      <Popover
                        open={openDeletePopover === (element.id || element._id)}
                        onOpenChange={(open) => {
                          setOpenDeletePopover(
                            open ? ((element.id || element._id) as string) : null,
                          );
                        }}
                      >
                        <PopoverTrigger asChild>
                          <button
                            className="text-red-500 hover:text-red-700"
                            onClick={(e) => {
                              e.stopPropagation();
                            }}
                          >
                            Delete
                          </button>
                        </PopoverTrigger>
                        <PopoverContent
                          className="z-50 w-fit rounded-lg border border-gray-200 bg-white p-2 shadow-lg"
                          align="end"
                          side="top"
                        >
                          <div className="w-fit">Are you sure you want to delete this element?</div>
                          <div className="mt-2 flex">
                            <button
                              className="mr-2 rounded bg-gray-200 px-3 py-1 text-sm text-gray-700 hover:bg-gray-300"
                              onClick={(e) => {
                                e.stopPropagation();
                                setOpenDeletePopover(null);
                              }}
                            >
                              Cancel
                            </button>
                            <button
                              className="rounded bg-red-600 px-3 py-1 text-sm text-white hover:bg-red-700"
                              onClick={() => handleDeleteElement(element.id || element._id)}
                            >
                              Delete
                            </button>
                          </div>
                        </PopoverContent>
                      </Popover>
                    </div>
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow className="border-t">
                <TableCell colSpan={6} className="p-4 text-center text-gray-400">
                  <div className="flex flex-col items-center">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="18"
                      height="18"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="mb-1"
                    >
                      <circle cx="12" cy="12" r="10"></circle>
                      <line x1="12" y1="8" x2="12" y2="12"></line>
                      <line x1="12" y1="16" x2="12.01" y2="16"></line>
                    </svg>
                    <span>No {actionType.label.toLowerCase()}s available</span>
                    <span className="mt-1 text-xs">
                      Draw a new {actionType.label.toLowerCase()} on the screen to add it to the
                      list
                    </span>
                  </div>
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}
