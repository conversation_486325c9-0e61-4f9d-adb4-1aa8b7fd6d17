const multer = require('multer');
const express = require('express');
const router = express.Router();
const JobSimulationAppController = require('~/server/controllers/JobSimulationAppController.js');
const { requireJwtAuth } = require('~/server/middleware');
const { storage } = require('~/server/routes/files/multer');

const uploadJobSimulation = multer({ storage });

router.get('', JobSimulationAppController.getAll);
router.get('/:id', JobSimulationAppController.getById);

router.use(requireJwtAuth);

router.delete('/:id', JobSimulationAppController.deleteById);
router.post('/by-ids', JobSimulationAppController.getByIds);
router.post('', uploadJobSimulation.single('file'), JobSimulationAppController.create);
router.patch('/:id', uploadJobSimulation.single('file'), JobSimulationAppController.update);

module.exports = router;
