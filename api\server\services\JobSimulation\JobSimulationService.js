const { uploadFileToS3 } = require('~/server/services/Files/S3/crud');

const { JobSimulation, JobSimulationProgress } = require('~/models');
const { getAgentByJobSimulationId } = require('~/models/Agent');
const BillionService = require('~/server/services/Billion/BillionService');

const JobSimulationService = {

  async getUserJobSimulationsInfo(params) {
    const jobs = await JobSimulation.getUserJobSimulationsInfo(params);
    const jobIds = jobs.map((job) => job.jobSimulationId);
    const progresses = await JobSimulationProgress.getProgresses({ jobSimulationIds: jobIds, email: params.email });
    const mapObjProgressByJobId = progresses.reduce((acc, curr) => {
      acc[curr.jobSimulationId] = curr;
      return acc;
    }, {});

    jobs.forEach((job) => {
      job.progress = mapObjProgressByJobId[job.jobSimulationId];
    });

    return jobs;
  },

  async getUserJobSimulationInfo(params) {
    const { jobSimulationId, email } = params;
    // TODO: don't use this function, it return many fields
    const job = await JobSimulation.getJobSimulation(params);
    const [progress, agent] = await Promise.all([
      JobSimulationProgress.getProgress({ jobSimulationId, email }),
      getAgentByJobSimulationId(jobSimulationId),
    ])

    job.forEach((job) => {
      job.progress = progress;
      job.agent = agent
    });

    return job;
  },

  async getAdminJobSimulations(params) {
    return await JobSimulation.getJobSimulations(Object.assign(params, { fields: [], statuses: ['public'] }));
  },

  async getPublicJobSimulations(params) {
    return await JobSimulation.getPublicJobSimulations(params);
  },

  async getAdminJobSimulation(jobSimulationId) {
    return await JobSimulation.getJobSimulation(jobSimulationId);
  },

  async updateLogo(jobSimulationId, req, file) {
    const uploadResult = await uploadFileToS3({ req, file, file_id: Date.now() });
    if (!uploadResult?.filepath) return null;

    const result = await JobSimulation.updateLogo(jobSimulationId, uploadResult.filepath);
    return result;
  },

  async updateCredentials(jobSimulationId, username, password) {
    return await JobSimulation.updateCredentials(jobSimulationId, username, password);
  },

  async getOrCreateProgress(data) {
    const jobSimulation = await JobSimulationService.getAdminJobSimulation(data.jobSimulationId);
    if (!jobSimulation || jobSimulation.status === 'inactive') {
      throw new Error('Job simulation not found');
    }
    // TODO: if the job is private, must provide the invitation code
    return await JobSimulationProgress.getOrCreate(Object.assign(data || {}, { jobStatus: jobSimulation.status || 'private' }));
  },

  async getProgress(jobSimulationId, email) {
    const data = await JobSimulationProgress.getProgress({ jobSimulationId, email: email });
    return data;
  },

  async saveProgressConversationId(jobSimulationId, email, conversationId) {
    return await JobSimulationProgress.saveConversationId({ jobSimulationId, email, conversationId });
  },

  async saveProgressIntakeId(jobSimulationId, email, intakeId) {
    return await JobSimulationProgress.saveIntakeId({ jobSimulationId, email, intakeId });
  },

  // TODO: create JobSimulationProgressService and move this logic to this file
  async updateProgress(jobSimulationId, email, data) {
    return await JobSimulationProgress.update({ jobSimulationId, email, data });
  },

  // TODO: create JobSimulationProgressService and move this logic to this file
  async updateProgressTasks(jobSimulationId, email) {
    const jobSimulation = await JobSimulationService.getAdminJobSimulation(jobSimulationId);
    if (!jobSimulation) {
      throw new Error('Job simulation not found');
    }
    const tasks = await BillionService.getTasks({ email, billionIntakeId: jobSimulation.billionIntakeId });
    if (!tasks?.length) {
      throw new Error('No tasks found');
    }
    const progressTasks = tasks.map((task) => ({
      id: task.taskId,
      title: task.taskName,
      status: task.status === 'passed' ? 'passed' : 'todo',
      completedAt: task.completedAt ? new Date(task.completedAt) : undefined,
    }));
    const totalTasks = progressTasks.length;
    const totalCompletedTasks = progressTasks.filter(task => task.status === 'passed').length;
    const status = totalCompletedTasks === totalTasks ? 'completed' : 'active';
    const completedAt = status === 'completed' ? new Date() : undefined;
    // TODO: Using AI to detect skills / scores that the user gained from the job simulation (each skill has a rating from 1 to 5)
    const skills = (status === 'completed' && !!jobSimulation.skills?.length)
      ? jobSimulation.skills.map((skill) => ({ name: skill, rating: Math.floor(Math.random() * (3)) + 3 }))
      : undefined;
    // TODO: For nowm random scores 98 to 100 when completed. In the future, use AI to detect the scores
    const scores = status === 'completed' ? Math.floor(Math.random() * 2) + 98 : undefined;
    return await JobSimulationProgress.update({ jobSimulationId, email, data: { tasks: progressTasks, totalTasks, totalCompletedTasks, status, completedAt, skills, scores } });
  },

  // TODO: create JobSimulationProgressService and move this logic to this file
  async updateProgressEmailReplies(jobSimulationId, email, emailId, reply) {
    return await JobSimulationProgress.model.findOneAndUpdate(
      { jobSimulationId, email, 'emails.id': emailId },
      { $push: { 'emails.$.replies': reply } },
      { new: true },
    ).lean();
  },
};

module.exports = JobSimulationService;
