import { useNavigate, useParams } from 'react-router-dom';
import ImageClick from './ImageClick';
import {
  useGetJobSimulationAppScreen,
  useListJobSimulationAppScreenByUserAndApp,
} from '~/data-provider/JobSimulation';
import { useAuthContext } from '~/hooks';
import { Button, Skeleton } from '~/components/ui';
import { useState } from 'react';
import { useToastContext } from '~/Providers';
import {
  useDeleleJobSimulationAppScreen,
  useUpdateJobSimulationAppScreen,
} from '~/data-provider/JobSimulation/mutations';
import store from '~/store';
import { useRecoilValue } from 'recoil';

export default function ScreenDetail() {
  const navigate = useNavigate();
  const { appId, screenId } = useParams();
  const { isAuthenticated } = useAuthContext();
  const { showToast } = useToastContext();
  const user = useRecoilValue(store.user);

  const [messageSuccess, setMessageSuccess] = useState('');
  const [messageError, setMessageError] = useState('');

  const {
    data: screen,
    isLoading,
    refetch,
  } = useGetJobSimulationAppScreen(screenId as string, '', {
    enabled: isAuthenticated && !!screenId,
  });

  const { data: dataScreens, refetch: refetchScreens } = useListJobSimulationAppScreenByUserAndApp({
    userId: user?.id as string,
    appId: appId as string,
    page: 1,
    limit: 100,
  });

  const currentScreenIndex = (dataScreens?.screens || [])?.findIndex(
    (s: any) => s._id === screenId,
  );

  const updateScreen = useUpdateJobSimulationAppScreen({
    onSuccess: () => {
      refetch();
      refetchScreens();
      showToast({
        message: messageSuccess,
        status: 'success',
      });
    },
    onError: () => {
      showToast({
        message: messageError,
        status: 'error',
      });
    },
  });

  const handleUpdate = (payload, type) => {
    if (type === 'add') {
      setMessageSuccess('Element added successfully');
      setMessageError('Failed to add element');
    } else if (type === 'delete') {
      setMessageSuccess('Element deleted successfully');
      setMessageError('Failed to delete element');
    } else if (type === 'deleteAll') {
      setMessageSuccess('All elements deleted successfully');
      setMessageError('Failed to delete all elements');
    } else if (type === 'update') {
      setMessageSuccess('Element updated successfully');
      setMessageError('Failed to update element');
    }
    updateScreen.mutate({
      id: screenId,
      userId: user?.id as string,
      appId: appId,
      bgColor: payload.bgColor || '',
      elements: payload.elements || [],
      placeholders: payload.placeholders || [],
      charts: payload.charts || [],
    });
  };

  const handleExitDetail = () => {
    navigate('/job-simulation/setting');
  };

  const handleGotoScreen = (id: string) => {
    navigate(`/job-simulation/setting/${appId}/${id}`);
  };

  return (
    <>
      <div className="p-8 dark:text-white">
        <div className="mb-6 flex gap-2">
          <Button onClick={handleExitDetail}>Exit</Button>
          <Button
            onClick={() => {
              if (currentScreenIndex > 0)
                handleGotoScreen(dataScreens.screens[currentScreenIndex - 1]._id);
            }}
            disabled={currentScreenIndex <= 0}
          >
            ← Back
          </Button>
          <Button
            onClick={() => {
              if (
                currentScreenIndex > -1 &&
                currentScreenIndex < (dataScreens?.screens || []).length - 1
              )
                handleGotoScreen(dataScreens.screens[currentScreenIndex + 1]._id);
            }}
            disabled={
              currentScreenIndex < 0 ||
              currentScreenIndex >= (dataScreens?.screens || []).length - 1
            }
          >
            Next →
          </Button>
        </div>

        {isLoading ? (
          <Skeleton className="h-[500px] w-full" />
        ) : screen ? (
          <ImageClick screens={dataScreens?.screens} screen={screen} onUpdate={handleUpdate} />
        ) : (
          <div className="mt-10 text-center text-gray-500">
            No screen found. Please select a valid screen.
          </div>
        )}
      </div>
    </>
  );
}
