const express = require('express');
const router = express.Router();
const { requireJwtAuth } = require('~/server/middleware');
const JobSimulationFeedbackController = require('~/server/controllers/JobSimulationFeedbackController.js');

router.use(requireJwtAuth);

router.get('/', JobSimulationFeedbackController.getByUserAndJobSimulation)
router.get('/list', JobSimulationFeedbackController.getByJobSimulation)
router.get('/:id', JobSimulationFeedbackController.getById);
router.post('/', JobSimulationFeedbackController.create);
router.patch('/:id', JobSimulationFeedbackController.update);
router.delete('/:id', JobSimulationFeedbackController.deleteById);

module.exports = router;