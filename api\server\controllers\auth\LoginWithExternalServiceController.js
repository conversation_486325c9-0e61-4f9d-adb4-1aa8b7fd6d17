const { setAuthTokens } = require('~/server/services/AuthService');
const { logger } = require('~/config');
const BillionService = require('~/server/services/Billion/BillionService');
const { SystemRoles } = require('librechat-data-provider');
const {
  createExternalUser
} = require('~/models/userMethods');

const loginWithExternalServiceController = async (req, res) => {
  try {
    const { token: serviceUserToken, externalService, env } = req.body;

    // TODO: for now, only allow ig
    if (!serviceUserToken || !externalService || !env || externalService !== 'ig') {
      return res.status(400).json({ message: 'Missing required fields' });
    }

    const resultVerify = await BillionService.verifyToken({ token: serviceUserToken, env });
    if (!resultVerify || !resultVerify.user) {
      return res.status(400).json({ message: 'Invalid token' });
    }

    const userData = {
      email: resultVerify.user.email,
      name: resultVerify.user.name,
      avatar: resultVerify.user.avatar,
      provider: externalService,
      role: SystemRoles.USER,
      igId: resultVerify.user.id,
      emailVerified: true,
    }

    const user = await createExternalUser(userData);


    const { password: _p, totpSecret: _t, __v, secretCode: _s, ...safeUser } = user.toObject();
    safeUser.id = user._id.toString();

    const token = await setAuthTokens(user._id, res);

    return res.status(200).send({ token, user: safeUser });
  } catch (err) {
    logger.error('[loginWithExternalServiceController]', err);
    return res.status(500).json({ message: 'Something went wrong' });
  }
};


module.exports = {
  loginWithExternalServiceController,
};
