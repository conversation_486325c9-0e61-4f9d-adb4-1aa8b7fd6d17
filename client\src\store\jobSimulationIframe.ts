import { atom } from 'recoil';

const jobSimulationIframe = atom<{
  url: string;
  type: 'virtual-world' | 'normal' | 'browser';
  visible: boolean;
} | null>({
  key: 'jobSimulationIframe',
  default: null,
});

const jobSimulationVirtualWorldMessage = atom<string | null>({
  key: 'jobSimulationVirtualWorldMessage',
  default: null,
});

const jobSimulationBrowserGoal = atom<string | null>({
  key: 'jobSimulationBrowserGoal',
  default: null,
});

export default {
  jobSimulationIframe,
  jobSimulationVirtualWorldMessage,
  jobSimulationBrowserGoal,
};
